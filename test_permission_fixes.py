#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试权限验证逻辑修复
"""
import re
from unittest.mock import Mock

from fastapi import Request
import pytest

from app.core.dependencies import AuthPermission
from app.core.exceptions.exception import AccessException
from app.schemas.system.auth_schema import Auth


class TestAuthPermissionFixes:
    """测试权限验证修复"""

    def test_wildcard_pattern_building(self):
        """测试通配符模式构建"""
        # 测试单级通配符
        pattern = AuthPermission._build_wildcard_pattern('/api/users/*')
        assert pattern == r'^/api/users/[^/]*$'

        # 测试多级通配符
        pattern = AuthPermission._build_wildcard_pattern('/api/**')
        assert pattern == r'^/api/.*$'

        # 测试混合通配符
        pattern = AuthPermission._build_wildcard_pattern('/api/*/admin/**')
        assert pattern == r'^/api/[^/]*/admin/.*$'

        # 测试特殊字符转义
        pattern = AuthPermission._build_wildcard_pattern('/api/users/[test]')
        assert pattern == r'^/api/users/\[test\]$'

    def test_wildcard_matching(self):
        """测试通配符匹配"""
        # 单级通配符测试
        pattern = AuthPermission._build_wildcard_pattern('/api/users/*')
        assert re.match(pattern, '/api/users/123')
        assert re.match(pattern, '/api/users/abc')
        assert not re.match(pattern, '/api/users/123/profile')  # 不应匹配多级
        assert not re.match(pattern, '/api/users/')  # 不应匹配空

        # 多级通配符测试
        pattern = AuthPermission._build_wildcard_pattern('/api/**')
        assert re.match(pattern, '/api/users')
        assert re.match(pattern, '/api/users/123')
        assert re.match(pattern, '/api/users/123/profile')
        assert not re.match(pattern, '/other/api')

    @pytest.mark.asyncio
    async def test_permission_verification_with_cache(self):
        """测试带缓存的权限验证"""
        # 创建模拟对象
        mock_user = Mock()
        mock_user.id = 1
        mock_user.roles = []

        mock_role = Mock()
        mock_role.is_admin = False
        mock_role.permissions = []

        mock_permission = Mock()
        mock_permission.code = 'user.read'
        mock_role.permissions = [mock_permission]
        mock_user.roles = [mock_role]

        mock_auth = Mock(spec=Auth)
        mock_auth.user = mock_user

        # 创建权限验证器
        auth_permission = AuthPermission(auto=False, permissions_code=['user.read'])

        # 第一次调用应该计算权限并缓存
        await auth_permission.verify_by_permission_code(mock_auth)

        # 验证缓存中有数据
        cache_key = f'user_permissions_{mock_user.id}'
        assert cache_key in auth_permission._permission_cache
        assert 'user.read' in auth_permission._permission_cache[cache_key]

        # 第二次调用应该使用缓存
        await auth_permission.verify_by_permission_code(mock_auth)

    @pytest.mark.asyncio
    async def test_admin_role_detection(self):
        """测试管理员角色检测"""
        # 创建普通用户
        mock_user = Mock()
        mock_user.id = 2

        mock_role = Mock()
        mock_role.is_admin = False
        mock_user.roles = [mock_role]

        mock_auth = Mock(spec=Auth)
        mock_auth.user = mock_user

        # 应该返回 False
        is_admin = await AuthPermission._is_super_admin(mock_auth)
        assert not is_admin

        # 创建管理员用户
        mock_admin_role = Mock()
        mock_admin_role.is_admin = True
        mock_user.roles = [mock_admin_role]

        # 应该返回 True
        is_admin = await AuthPermission._is_super_admin(mock_auth)
        assert is_admin

    @pytest.mark.asyncio
    async def test_no_permission_specified_should_deny(self):
        """测试未指定权限时应该拒绝访问"""
        mock_user = Mock()
        mock_user.id = 2
        mock_user.roles = []

        mock_auth = Mock(spec=Auth)
        mock_auth.user = mock_user

        mock_request = Mock(spec=Request)

        # 创建权限验证器，既不是auto也没有指定权限码
        auth_permission = AuthPermission(auto=False, permissions_code=None)

        # 应该抛出 AccessException
        with pytest.raises(AccessException):
            await auth_permission(mock_request, mock_auth)

    @pytest.mark.asyncio
    async def test_path_verification_exact_match(self):
        """测试路径精确匹配"""
        mock_user = Mock()
        mock_user.id = 2

        mock_role = Mock()
        mock_role.is_admin = False

        mock_permission = Mock()
        mock_permission.path = '/api/users'
        mock_permission.method = 'GET'
        mock_role.permissions = [mock_permission]
        mock_user.roles = [mock_role]

        mock_auth = Mock(spec=Auth)
        mock_auth.user = mock_user

        mock_request = Mock(spec=Request)
        mock_request.scope = {'path': '/api/users'}
        mock_request.method = 'GET'

        # 应该通过验证
        await AuthPermission.verify_by_request_path(mock_request, mock_auth)

    @pytest.mark.asyncio
    async def test_path_verification_wildcard_match(self):
        """测试路径通配符匹配"""
        mock_user = Mock()
        mock_user.id = 2

        mock_role = Mock()
        mock_role.is_admin = False

        mock_permission = Mock()
        mock_permission.path = '/api/users/*'
        mock_permission.method = 'GET'
        mock_role.permissions = [mock_permission]
        mock_user.roles = [mock_role]

        mock_auth = Mock(spec=Auth)
        mock_auth.user = mock_user

        mock_request = Mock(spec=Request)
        mock_request.scope = {'path': '/api/users/123'}
        mock_request.method = 'GET'

        # 应该通过验证
        await AuthPermission.verify_by_request_path(mock_request, mock_auth)

    @pytest.mark.asyncio
    async def test_path_verification_no_permission(self):
        """测试无权限时应该拒绝"""
        mock_user = Mock()
        mock_user.id = 2

        mock_role = Mock()
        mock_role.is_admin = False
        mock_role.permissions = []  # 没有权限
        mock_user.roles = [mock_role]

        mock_auth = Mock(spec=Auth)
        mock_auth.user = mock_user

        mock_request = Mock(spec=Request)
        mock_request.scope = {'path': '/api/users'}
        mock_request.method = 'GET'

        # 应该抛出 AccessException
        with pytest.raises(AccessException):
            await AuthPermission.verify_by_request_path(mock_request, mock_auth)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
