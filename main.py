
from app.app import shell_app


# from app.app import create_app
# from app.core.config import app_settings
# app = create_app()

if __name__ == '__main__':
    shell_app()

    # import uvicorn
    #
    # uvicorn.run(
    #     app='main:app',
    #     host=app_settings.APP_SERVER_HOST,
    #     port=app_settings.APP_SERVER_PORT,
    #     root_path=app_settings.APP_API_PREFIX,
    #     reload=app_settings.APP_RELOAD,
    #     lifespan="on",  # 强制启用 lifespan 支持
    #     # factory=True
    # )
