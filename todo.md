
先写个模型和写个几个接口测试一下，

日志记录参考：
https://chatgpt.com/share/687a6591-3d8c-800d-a0df-31b9b475835c





https://zhuanlan.zhihu.com/p/1897321634811057704?share_code=rTfRuUny6D5&utm_psn=1934052175815907291

可以参考若依框架的SQL：https://github1s.com/yangzongzhuan/RuoYi/blob/master/sql/ry_20250416.sql



- [x] 实现 token和refreshToken
- [x] 实现验证码功能
- [ ] 实现IP限制(中间件)
- [x] 实现登录错误密码限制，超过密码错误次数之后一定时间内不允许登录
- [x] 实现同一个账号只能登录一次
- [ ] 记录日志到数据库中？
- [ ] 用户在线状态(Redis)
- [ ] todo 调整，用户，角色，员工的关系，使得用户和角色关联，而员工则作为用户的信息的一种补充？ 或者说使用userProfile?作为用户基础信息？
- [ ] 权限缓存？
- [ ] 实现定时任务
- [ ] 字典管理
- [ ] 系统运行状态
- [ ] 接口加密(中间件？？)
- [ ] 表单修改历史追踪(类似odo的mail.thread，https://chatgpt.com/share/688c3157-aa6c-800d-9501-f0c9ffe4b7c4    https://chat.deepseek.com/a/chat/s/d271e018-dc21-4963-9f94-01203a515cc4)
- [ ] 数据范围权限配置，数据操作权限






包含字段过滤(动态生Pydantic模型)，行级过滤，RBAC基础模型
https://chatgpt.com/share/688f8137-d36c-800d-9e4f-48643e922a58


可以参考这个项目的UI设计：
https://gitee.com/battcn/wemirr-platform

学习研究dash，flet, Taipy

res_users -- 用户
res_groups -- 用户组
ir_model_access -- 访问控制列表（Access Control List，ACL）read write create unlink
ir.rule -- 记录规则（Record Rules）

ir_model_field
ir_ui_menus
ir_ui_view
ir_action
ir.module.module
ir_model_data
ir_filters

Odoo 权限是多层过滤 + 多组合并机制：
1.	先判断是否有访问模型的权限（ACL）
2.	再判断记录是否符合Record Rule
3.	再判断字段是否可见/可编辑
4.	如果用户属于多个组，会合并所有组的权限，只要有一个组拥有某权限即为允许

❗ 注意：
•	Record Rule 是「与」关系（只要有一条规则限制访问，就无法访问）
•	访问控制是「或」关系（多个组权限是合并的）




```python
# 权限缓存
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from fastapi_cache.decorator import cache

@app.get("/permissions")
@cache(expire=3600)  # 缓存1小时
async def get_permissions(db: Session = Depends(get_db)):
    return db.query(models.Permission).all()

```

实现日志记录到数据库中(根据配置来决定是否将日志存入数据库？) 、实现权限验证拦截、其他一些拦截器、接口耗时统计、请求入口拦截，限流
可选方案，1. 装饰器， 2. route_class，3、Depends，4. 中间件

✅ 推荐组合用法（实践经验）
•	🛡️ 权限 / 用户依赖：用 Depends(...)
•	📊 统一日志 / 性能统计 / 响应格式：用 route_class
•	🔍 特殊业务逻辑（审计、统计、限流）：用 @decorator
```python
router = APIRouter(
    route_class=TracingRoute,
    dependencies=[Depends(rbac_check(["admin"]))]
)

@router.get("/settings")
@audit_log("访问系统设置")
async def settings(...):
    ...

```


使用目标       推荐方式
统一响应格式、包装 API 返回值  ✅ route_class=CustomRoute
权限验证、租户隔离、参数注入  ✅ Depends(get_current_user)
请求耗时统计、链路追踪  ✅ route_class 或 Middleware
打点、日志、审计（局部）  ✅ @audit_log 装饰器
全局请求记录、错误监控  ✅ Middleware
插件模块扩展（如“AI接口专属 trace”）  ✅ APIRouter(route_class=...) per module


Request In
│
▼
[Middleware]
│
▼
[route_class: APIRoute.get_route_handler()]
│
▼
[Depends() 注入参数]
│
▼
[@decorator 包装函数]
│
▼
[endpoint 函数执行]
│
▼
[Response 返回前经过 route_class / middleware]


模块       实现方式
🌍 全局入口日志、异常监控、限流  ✅ Middleware
👤 用户注入、权限校验  ✅ Depends
📦 插件响应结构统一包装  ✅ route_class per plugin
📊 局部埋点、审计  ✅ 自定义装饰器



Gunicorn+Uvicorn部署应用


无论是使用函数或者类来定义都无法对Depends传参， 可以通过类的方式重写—__call__方法；
还可以利用FastAPI框架提供的Security模块，该模块也可以实现类似的依赖注入项传参



