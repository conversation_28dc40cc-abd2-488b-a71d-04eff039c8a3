""""init1"

Revision ID: 880f5c1d7d25
Revises: 
Create Date: 2025-08-10 20:37:10.105815

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision: str = '880f5c1d7d25'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('res_user',
    sa.Column('login', sa.String(length=50), nullable=False, comment='登录名'),
    sa.Column('password', sa.String(length=1000), nullable=False, comment='密码'),
    sa.Column('nickname', sa.String(length=50), nullable=True, comment='昵称'),
    sa.Column('email', sa.String(length=100), nullable=True, comment='邮箱'),
    sa.Column('phone', sa.String(length=20), nullable=True, comment='手机'),
    sa.Column('avatar', sa.String(length=255), nullable=True, comment='头像'),
    sa.Column('sex', sa.Integer(), nullable=True, comment='性别,0:其他,1:男,2:女'),
    sa.Column('summary', sa.Text(), nullable=True, comment='个人简介'),
    sa.Column('lang', sa.String(length=50), nullable=True, comment='语言'),
    sa.Column('tz', sa.String(length=50), nullable=True, comment='时区'),
    sa.Column('last_login', sa.DateTime(), nullable=True, comment='最近登录时间'),
    sa.Column('login_ip', sa.String(length=128), nullable=True, comment='最后登录IP'),
    sa.Column('create_uid', sa.BIGINT(), nullable=True, comment='创建人'),
    sa.Column('write_uid', sa.BIGINT(), nullable=True, comment='更新人'),
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='唯一ID'),
    sa.Column('create_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('write_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('active', sa.Boolean(), server_default='true', nullable=False, comment='是否归档:True=未删除,False=删除'),
    sa.Column('remark', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['create_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['write_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='系统用户'
    )
    op.create_index(op.f('ix_res_user_create_uid'), 'res_user', ['create_uid'], unique=False)
    op.create_index(op.f('ix_res_user_email'), 'res_user', ['email'], unique=False)
    op.create_index(op.f('ix_res_user_id'), 'res_user', ['id'], unique=True)
    op.create_index(op.f('ix_res_user_login'), 'res_user', ['login'], unique=True)
    op.create_index(op.f('ix_res_user_nickname'), 'res_user', ['nickname'], unique=False)
    op.create_index(op.f('ix_res_user_phone'), 'res_user', ['phone'], unique=False)
    op.create_index(op.f('ix_res_user_write_uid'), 'res_user', ['write_uid'], unique=False)
    op.create_table('res_dept',
    sa.Column('name', sa.String(length=50), nullable=False, comment='名称'),
    sa.Column('order', sa.Integer(), nullable=False, comment='排序'),
    sa.Column('parent_id', sa.BIGINT(), nullable=True, comment='父级部门ID'),
    sa.Column('create_uid', sa.BIGINT(), nullable=True, comment='创建人'),
    sa.Column('write_uid', sa.BIGINT(), nullable=True, comment='更新人'),
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='唯一ID'),
    sa.Column('create_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('write_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('active', sa.Boolean(), server_default='true', nullable=False, comment='是否归档:True=未删除,False=删除'),
    sa.Column('remark', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['create_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['parent_id'], ['res_dept.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['write_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='部门表'
    )
    op.create_index(op.f('ix_res_dept_create_uid'), 'res_dept', ['create_uid'], unique=False)
    op.create_index(op.f('ix_res_dept_id'), 'res_dept', ['id'], unique=True)
    op.create_index(op.f('ix_res_dept_name'), 'res_dept', ['name'], unique=False)
    op.create_index(op.f('ix_res_dept_parent_id'), 'res_dept', ['parent_id'], unique=False)
    op.create_index(op.f('ix_res_dept_write_uid'), 'res_dept', ['write_uid'], unique=False)
    op.create_table('res_menu',
    sa.Column('name', sa.String(length=50), nullable=False, comment='名称'),
    sa.Column('menu_type', sa.String(length=32), nullable=True, comment='菜单类型'),
    sa.Column('icon', sa.String(length=255), nullable=True, comment='图标'),
    sa.Column('route_name', sa.String(length=50), nullable=True, comment='路由名称'),
    sa.Column('route_path', sa.String(length=50), nullable=True, comment='路由路径'),
    sa.Column('component_path', sa.String(length=50), nullable=True, comment='组件路径'),
    sa.Column('redirect', sa.String(length=50), nullable=True, comment='重定向'),
    sa.Column('meta', sa.Text(), nullable=True, comment='元数据'),
    sa.Column('order', sa.Integer(), nullable=False, comment='显示排序'),
    sa.Column('description', sa.Text(), nullable=True, comment='备注'),
    sa.Column('parent_id', sa.BIGINT(), nullable=True, comment='上级菜单'),
    sa.Column('create_uid', sa.BIGINT(), nullable=True, comment='创建人'),
    sa.Column('write_uid', sa.BIGINT(), nullable=True, comment='更新人'),
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='唯一ID'),
    sa.Column('create_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('write_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('active', sa.Boolean(), server_default='true', nullable=False, comment='是否归档:True=未删除,False=删除'),
    sa.Column('remark', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['create_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['parent_id'], ['res_menu.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['write_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='菜单表'
    )
    op.create_index(op.f('ix_res_menu_create_uid'), 'res_menu', ['create_uid'], unique=False)
    op.create_index(op.f('ix_res_menu_id'), 'res_menu', ['id'], unique=True)
    op.create_index(op.f('ix_res_menu_name'), 'res_menu', ['name'], unique=True)
    op.create_index(op.f('ix_res_menu_parent_id'), 'res_menu', ['parent_id'], unique=False)
    op.create_index(op.f('ix_res_menu_write_uid'), 'res_menu', ['write_uid'], unique=False)
    op.create_table('res_permission',
    sa.Column('code', sa.String(length=32), nullable=False, comment='权限代码'),
    sa.Column('name', sa.String(length=32), nullable=False, comment='权限名称'),
    sa.Column('path', sa.String(length=255), nullable=True, comment='路径'),
    sa.Column('method', sa.String(length=32), nullable=True, comment='方法'),
    sa.Column('description', sa.Text(), nullable=True, comment='描述'),
    sa.Column('category', sa.String(length=32), nullable=True, comment='权限类别'),
    sa.Column('parent_id', sa.BIGINT(), nullable=True, comment='上级权限'),
    sa.Column('create_uid', sa.BIGINT(), nullable=True, comment='创建人'),
    sa.Column('write_uid', sa.BIGINT(), nullable=True, comment='更新人'),
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='唯一ID'),
    sa.Column('create_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('write_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('active', sa.Boolean(), server_default='true', nullable=False, comment='是否归档:True=未删除,False=删除'),
    sa.Column('remark', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['create_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['parent_id'], ['res_permission.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['write_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='权限表'
    )
    op.create_index(op.f('ix_res_permission_code'), 'res_permission', ['code'], unique=True)
    op.create_index(op.f('ix_res_permission_create_uid'), 'res_permission', ['create_uid'], unique=False)
    op.create_index(op.f('ix_res_permission_id'), 'res_permission', ['id'], unique=True)
    op.create_index(op.f('ix_res_permission_name'), 'res_permission', ['name'], unique=True)
    op.create_index(op.f('ix_res_permission_parent_id'), 'res_permission', ['parent_id'], unique=False)
    op.create_index(op.f('ix_res_permission_write_uid'), 'res_permission', ['write_uid'], unique=False)
    op.create_table('res_role',
    sa.Column('code', sa.String(length=40), nullable=False, comment='角色代码'),
    sa.Column('name', sa.String(length=40), nullable=False, comment='角色名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='描述'),
    sa.Column('order', sa.Integer(), nullable=False, comment='显示排序'),
    sa.Column('data_scope', sa.Integer(), nullable=False, comment='数据权限'),
    sa.Column('create_uid', sa.BIGINT(), nullable=True, comment='创建人'),
    sa.Column('write_uid', sa.BIGINT(), nullable=True, comment='更新人'),
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='唯一ID'),
    sa.Column('create_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('write_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('active', sa.Boolean(), server_default='true', nullable=False, comment='是否归档:True=未删除,False=删除'),
    sa.Column('remark', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['create_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['write_uid'], ['res_user.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='角色表'
    )
    op.create_index(op.f('ix_res_role_code'), 'res_role', ['code'], unique=True)
    op.create_index(op.f('ix_res_role_create_uid'), 'res_role', ['create_uid'], unique=False)
    op.create_index(op.f('ix_res_role_id'), 'res_role', ['id'], unique=True)
    op.create_index(op.f('ix_res_role_name'), 'res_role', ['name'], unique=True)
    op.create_index(op.f('ix_res_role_write_uid'), 'res_role', ['write_uid'], unique=False)
    op.create_table('res_role_menu_rel',
    sa.Column('role_id', sa.BIGINT(), nullable=False, comment='角色ID'),
    sa.Column('menu_id', sa.BIGINT(), nullable=False, comment='菜单ID'),
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='唯一ID'),
    sa.Column('create_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('write_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('active', sa.Boolean(), server_default='true', nullable=False, comment='是否归档:True=未删除,False=删除'),
    sa.Column('remark', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['menu_id'], ['res_menu.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['role_id'], ['res_role.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('role_id', 'menu_id', 'id'),
    comment='角色菜单关联表'
    )
    op.create_index(op.f('ix_res_role_menu_rel_id'), 'res_role_menu_rel', ['id'], unique=True)
    op.create_index(op.f('ix_res_role_menu_rel_menu_id'), 'res_role_menu_rel', ['menu_id'], unique=False)
    op.create_index(op.f('ix_res_role_menu_rel_role_id'), 'res_role_menu_rel', ['role_id'], unique=False)
    op.create_table('res_role_permissions_rel',
    sa.Column('role_id', sa.BIGINT(), nullable=False, comment='角色ID'),
    sa.Column('permission_id', sa.BIGINT(), nullable=False, comment='权限ID'),
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='唯一ID'),
    sa.Column('create_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('write_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('active', sa.Boolean(), server_default='true', nullable=False, comment='是否归档:True=未删除,False=删除'),
    sa.Column('remark', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['permission_id'], ['res_permission.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['role_id'], ['res_role.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('role_id', 'permission_id', 'id'),
    comment='角色权限关系表'
    )
    op.create_index(op.f('ix_res_role_permissions_rel_id'), 'res_role_permissions_rel', ['id'], unique=True)
    op.create_index(op.f('ix_res_role_permissions_rel_permission_id'), 'res_role_permissions_rel', ['permission_id'], unique=False)
    op.create_index(op.f('ix_res_role_permissions_rel_role_id'), 'res_role_permissions_rel', ['role_id'], unique=False)
    op.create_table('res_user_role_rel',
    sa.Column('user_id', sa.BIGINT(), nullable=False, comment='用户ID'),
    sa.Column('role_id', sa.BIGINT(), nullable=False, comment='角色ID'),
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False, comment='唯一ID'),
    sa.Column('create_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('write_date', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('active', sa.Boolean(), server_default='true', nullable=False, comment='是否归档:True=未删除,False=删除'),
    sa.Column('remark', sa.Text(), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['role_id'], ['res_role.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['res_user.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('user_id', 'role_id', 'id'),
    comment='用户角色关联表'
    )
    op.create_index(op.f('ix_res_user_role_rel_id'), 'res_user_role_rel', ['id'], unique=True)
    op.create_index(op.f('ix_res_user_role_rel_role_id'), 'res_user_role_rel', ['role_id'], unique=False)
    op.create_index(op.f('ix_res_user_role_rel_user_id'), 'res_user_role_rel', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_res_user_role_rel_user_id'), table_name='res_user_role_rel')
    op.drop_index(op.f('ix_res_user_role_rel_role_id'), table_name='res_user_role_rel')
    op.drop_index(op.f('ix_res_user_role_rel_id'), table_name='res_user_role_rel')
    op.drop_table('res_user_role_rel')
    op.drop_index(op.f('ix_res_role_permissions_rel_role_id'), table_name='res_role_permissions_rel')
    op.drop_index(op.f('ix_res_role_permissions_rel_permission_id'), table_name='res_role_permissions_rel')
    op.drop_index(op.f('ix_res_role_permissions_rel_id'), table_name='res_role_permissions_rel')
    op.drop_table('res_role_permissions_rel')
    op.drop_index(op.f('ix_res_role_menu_rel_role_id'), table_name='res_role_menu_rel')
    op.drop_index(op.f('ix_res_role_menu_rel_menu_id'), table_name='res_role_menu_rel')
    op.drop_index(op.f('ix_res_role_menu_rel_id'), table_name='res_role_menu_rel')
    op.drop_table('res_role_menu_rel')
    op.drop_index(op.f('ix_res_role_write_uid'), table_name='res_role')
    op.drop_index(op.f('ix_res_role_name'), table_name='res_role')
    op.drop_index(op.f('ix_res_role_id'), table_name='res_role')
    op.drop_index(op.f('ix_res_role_create_uid'), table_name='res_role')
    op.drop_index(op.f('ix_res_role_code'), table_name='res_role')
    op.drop_table('res_role')
    op.drop_index(op.f('ix_res_permission_write_uid'), table_name='res_permission')
    op.drop_index(op.f('ix_res_permission_parent_id'), table_name='res_permission')
    op.drop_index(op.f('ix_res_permission_name'), table_name='res_permission')
    op.drop_index(op.f('ix_res_permission_id'), table_name='res_permission')
    op.drop_index(op.f('ix_res_permission_create_uid'), table_name='res_permission')
    op.drop_index(op.f('ix_res_permission_code'), table_name='res_permission')
    op.drop_table('res_permission')
    op.drop_index(op.f('ix_res_menu_write_uid'), table_name='res_menu')
    op.drop_index(op.f('ix_res_menu_parent_id'), table_name='res_menu')
    op.drop_index(op.f('ix_res_menu_name'), table_name='res_menu')
    op.drop_index(op.f('ix_res_menu_id'), table_name='res_menu')
    op.drop_index(op.f('ix_res_menu_create_uid'), table_name='res_menu')
    op.drop_table('res_menu')
    op.drop_index(op.f('ix_res_dept_write_uid'), table_name='res_dept')
    op.drop_index(op.f('ix_res_dept_parent_id'), table_name='res_dept')
    op.drop_index(op.f('ix_res_dept_name'), table_name='res_dept')
    op.drop_index(op.f('ix_res_dept_id'), table_name='res_dept')
    op.drop_index(op.f('ix_res_dept_create_uid'), table_name='res_dept')
    op.drop_table('res_dept')
    op.drop_index(op.f('ix_res_user_write_uid'), table_name='res_user')
    op.drop_index(op.f('ix_res_user_phone'), table_name='res_user')
    op.drop_index(op.f('ix_res_user_nickname'), table_name='res_user')
    op.drop_index(op.f('ix_res_user_login'), table_name='res_user')
    op.drop_index(op.f('ix_res_user_id'), table_name='res_user')
    op.drop_index(op.f('ix_res_user_email'), table_name='res_user')
    op.drop_index(op.f('ix_res_user_create_uid'), table_name='res_user')
    op.drop_table('res_user')
    # ### end Alembic commands ###
