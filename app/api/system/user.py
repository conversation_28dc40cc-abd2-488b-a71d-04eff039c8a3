# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Query
from fastapi.params import Depends
from fastapi.responses import JSONResponse

from app.api.params import PaginationQueryParams, UserQueryParams
from app.core.dependencies import AuthPermission, get_current_user
from app.core.response import SuccessResp
from app.schemas.system.auth_schema import Auth
from app.schemas.system.user_schema import UserBatchSetAvailable, UserCreate, UserRolesSetting, UserUpdate
from app.services.system.user_service import UserService


router = APIRouter()


@router.get('/current/info', summary='查询当前用户信息', description='查询当前用户信息')
async def get_current_user_info(auth: Auth = Depends(get_current_user)) -> JSONResponse:
    data = await UserService.get_current_user_info(auth)
    return SuccessResp(detail=data)


@router.post('/current/avatar/update', summary='上传当前用户头像', description='上传当前用户头像')
async def update_current_user_avatar(auth: Auth = Depends(get_current_user)) -> JSONResponse:
    detail = await UserService.update_current_user_avatar(auth)
    return SuccessResp(detail=detail)


@router.post('/current/password/update', summary='更新当前用户密码', description='更新当前用户密码')
async def update_current_user_password(auth: Auth = Depends(get_current_user)) -> JSONResponse:
    detail = await UserService.update_current_user_password(auth)
    return SuccessResp(detail=detail)


# 使用route_name存储权限码，可以在应用启动的时候自动同步到数据库中
@router.get('', name='system:user:list', summary='查询用户列表', description='查询用户列表')
async def get_user_list(
    page_query: Annotated[PaginationQueryParams, Query()],
    user_query: Annotated[UserQueryParams, Query()],
    auth: Auth = Depends(AuthPermission(auto=True,check_data_scope=True)),
) -> JSONResponse:
    print(page_query)
    print(user_query)

    detail = await UserService.get_user_list(page_query, user_query, auth)
    return SuccessResp(detail=detail)


@router.get('/{user_id}', name='system:user:detail', summary='用户详情', description='用户详情')
async def get_user_detail(user_id: int, auth: Auth = Depends(AuthPermission(auto=True,check_data_scope=True))) -> JSONResponse:
    detail = await UserService.get_detail_by_id(user_id, auth)
    return SuccessResp(detail=detail)


@router.post('', name='system:user:create', summary='创建用户', description='创建用户')
async def create_user(user_form: UserCreate, auth: Auth = Depends(AuthPermission(auto=True,check_data_scope=True))) -> JSONResponse:
    detail = await UserService.create_user(user_form, auth)
    return SuccessResp(detail=detail)


@router.put('', name='system:user:update', summary='更新用户', description='更新用户')
async def update_user(user_id: int, user_form: UserUpdate, auth: Auth = Depends(AuthPermission(auto=True,check_data_scope=True))) -> JSONResponse:
    detail = await UserService.update_user(user_id, user_form, auth)
    return SuccessResp(detail=detail)


@router.delete('/{user_id}', name='system:user:delete', summary='删除用户', description='删除用户')
async def delete_user(user_id: int, auth: Auth = Depends(AuthPermission(auto=True,check_data_scope=True))) -> JSONResponse:
    detail = await UserService.delete_user(user_id, auth)
    return SuccessResp(detail=detail)


@router.post('/roles', name='system:user:assign_role', summary='分配用户角色', description='分配角色')
async def assign_roles(user_role: UserRolesSetting, auth: Auth = Depends(AuthPermission(auto=True,check_data_scope=True))) -> JSONResponse:
    detail = await UserService.assign_roles(user_role, auth)
    return SuccessResp(detail=detail)


# todo 移动到权限路由中
###############################################################
@router.get('/{user_id}/permission', name='system:user:permission', summary='获取用户权限', description='获取用户权限')
async def get_user_permissions(user_id: int, auth: Auth = Depends(AuthPermission)) -> JSONResponse:
    detail = await UserService.get_user_roles(user_id, auth)
    return SuccessResp(detail=detail)


@router.get('/{user_id}/role', name='system:user:role', summary='获取用户角色', description='获取用户角色')
async def get_user_roles(user_id: int, auth: Auth = Depends(AuthPermission)) -> JSONResponse:
    detail = await UserService.get_user_roles(user_id, auth)
    return SuccessResp(detail=detail)


###############################################################
@router.post('/batch/enable', name='system:user:enable', summary='批量启用用户', description='批量启用用户')
async def enable_user(data: UserBatchSetAvailable, auth: Auth = Depends(AuthPermission)) -> JSONResponse:
    detail = await UserService.enable_user(data, auth)
    return SuccessResp(detail=detail)


@router.put('/batch/disable', name='system:user:disable', summary='禁用用户', description='禁用用户')
async def disable_user(data: UserBatchSetAvailable, auth: Auth = Depends(AuthPermission)) -> JSONResponse:
    detail = await UserService.disable_user(data, auth)
    return SuccessResp(detail=detail)
