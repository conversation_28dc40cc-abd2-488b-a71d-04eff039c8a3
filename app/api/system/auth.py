# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Union
import uuid

from fastapi import APIRouter, Request
from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import J<PERSON>NResponse

from app.core.config import app_settings, secret_settings
from app.core.dependencies import depends_get_db_session
from app.core.enums import RedisKeyEnum
from app.core.response import SuccessResp
from app.core.security import CustomOAuth2PasswordRequestForm
from app.schemas.system.auth_schema import CaptchaOut, JwtOut, RefreshTokenPayload
from app.services.system.auth_service import CaptchaService, LoginService


router = APIRouter()


@router.post('/login', response_model=JwtOut, summary='登录', description='登录')
async def login(request: Request,
                login_form: CustomOAuth2PasswordRequestForm = Depends(),
                db: AsyncSession = Depends(depends_get_db_session)
                ) -> Union[JSONResponse, Dict]:
    """
    登录
    :param request:
    :param login_form:
    :param db:
    :return:
    """
    await LoginService.check_captcha(request=request, login_form=login_form)
    user = await LoginService.authenticate_user(request, login_form, db)
    session_id = str(uuid.uuid4())
    login_token = await LoginService.create_token(user.login, session_id=session_id)
    # 如果已经登录了，再次登录则原来的token失效，
    # 如果允许同个账号多次登录，只需要使用uuid作为唯一key，如果不允许的话，就用当前用户ID作为key,这样每次登录的时候原来的token就会失效
    access_redis_key = user.id
    if app_settings.APP_SAME_TIME_LOGIN:
        access_redis_key = session_id

    access_token_key = RedisKeyEnum.ACCESS_TOKEN.key.format(key=access_redis_key)
    await request.app.state.redis.set(access_token_key, login_token.access_token, ex=timedelta(minutes=secret_settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES))

    # 如果是从API文档中来的，需要直接
    if app_settings.APP_DOCS_URL in request.headers.get('referer', ''):
        return login_token.model_dump()

    return SuccessResp(detail=login_token.model_dump(), message='登录成功')


@router.post('/refresh/token', response_model=JwtOut, summary='刷新token', description='刷新token')
async def refresh_token(payload: RefreshTokenPayload) -> JSONResponse:
    new_token = await LoginService.refresh_token(payload.refresh_token)
    return SuccessResp(detail=new_token.model_dump())


@router.get('/captcha', response_model=CaptchaOut, summary='获取验证码', description='获取登录验证码')
async def get_captcha(request: Request) -> JSONResponse:
    captcha = await CaptchaService.get_captcha(request)
    return SuccessResp(detail=captcha)
