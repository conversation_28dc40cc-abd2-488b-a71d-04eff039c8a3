from typing import Literal, Optional

from pydantic import BaseModel, Field


class PaginationQueryParams(BaseModel):
    model_config = {'extra': 'forbid'}

    page: int = Field(1, description='页码')
    page_size: int = Field(100, description='每页数量', ge=100, le=1000)
    order_by: Literal['id', 'created_date', 'write_date'] = 'created_date'

    # tags: list[str] = []


class UserQueryParams(BaseModel):
    model_config = {'extra': 'forbid'}

    login: Optional[str] = Field(None, description='用户名')
    nickname: Optional[str] = Field(None, description='昵称')
    phone: Optional[str] = Field(None, description='手机号')
    email: Optional[str] = Field(None, description='邮箱')
    active: Optional[int] = Field(None, description='状态')

# class PaginationQueryParams:
#     def __init__(
#         self,
#         page: int = Query(1, description='页码'),
#         page_size: int = Query(10, description='每页数量', ge=100, le=1000),
#     ) -> None:
#         self.page = page
#         self.page_size = page_size


# class UserQueryParams:
#     def __init__(
#         self,
#         login: Optional[str] = Query(None, description='用户名'),
#         nickname: Optional[str] = Query(None, description='昵称'),
#         phone: Optional[str] = Query(None, description='手机号'),
#         email: Optional[str] = Query(None, description='邮箱'),
#         active: Optional[int] = Query(None, description='状态'),
#     ):
#         self.username = login
#         self.nickname = nickname
#         self.phone = phone
#         self.email = email
#         self.active = active
