# -*-coding:utf-8 -*-
from fastapi import FastAPI
import typer
import uvicorn

from app.core.config import app_settings
from app.core.init_app import (
    lifespan,
    register_exception,
    register_hook,
    register_middlewares,
    register_router,
    register_static_file,
    reset_api_docs,
)
from app.scripts.upgrade.main import Upgrade


def create_app() -> FastAPI:
    """
    生成FatAPI对象
    :return:
    """
    app = FastAPI(**app_settings.get_backend_app_attributes, lifespan=lifespan)

    # 跨域设置
    register_middlewares(app)

    # 注册路由
    register_router(app)

    # 注册捕获异常
    register_exception(app)

    # 请求拦截
    register_hook(app)

    # 注册静态文件
    register_static_file(app)
    # 修复Redoc API文档CDN无法访问的问题
    reset_api_docs()

    return app


shell_app = typer.Typer()


@shell_app.command()
def start():
    """
    启动服务
    :return:
    """
    uvicorn.run(
        app='app.app:create_app',
        host=app_settings.APP_SERVER_HOST,
        port=app_settings.APP_SERVER_PORT,
        root_path=app_settings.APP_API_PREFIX,
        reload=app_settings.APP_RELOAD,
        lifespan='on',  # 强制启用 lifespan 支持
        factory=True
    )


@shell_app.command()
def init():
    """
    初始化数据
    :return:
    """
    pass
    # data = InitializeData()
    # data.run()


@shell_app.command()
def upgrade(message):
    """
    升级数据库表
    :return:
    """
    Upgrade().upgrade_table(message)


@shell_app.command()
def downgrade(revision):
    """
    数据库表降级,逐级回退
    :param revision: Alembic版本号
    :return:
    """
    Upgrade().downgrade(revision)
