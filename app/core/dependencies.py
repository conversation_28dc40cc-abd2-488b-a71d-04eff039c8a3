# -*- coding: utf-8 -*-
import re
from typing import AsyncGenerator, List, Optional

from fastapi import Depends, Request, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.async_database import Async<PERSON>ess<PERSON><PERSON>ocal
from app.core.exceptions.exception import AccessEx<PERSON>, TdumpException, TokenAuthException
from app.core.logger import tdump_log
from app.core.security import OAuth2Schema, decode_access_token
from app.schemas.system.auth_schema import Auth
from app.services.system.user_service import UserService


async def depends_get_db_session() -> AsyncGenerator[AsyncSession, None]:
    db_session = None
    try:
        db_session = AsyncSessionLocal()
        yield db_session
        await db_session.commit()
    except SQLAlchemyError as ex:
        await db_session.rollback()
        raise ex
    finally:
        await db_session.close()


async def get_current_user(
    request: Request, token: str = Depends(OAuth2Schema), session: AsyncSession = Depends(depends_get_db_session)
) -> Auth:
    token_payload = decode_access_token(token)
    if token_payload.is_refresh:
        raise TokenAuthException(message='非法的凭证')

    login_name = token_payload.sub
    auth = Auth(session=session)
    user = await UserService.get_detail_by_login(login_name, auth)
    if not user.active:
        raise TdumpException(message='用户已停用', status_code=status.HTTP_403_FORBIDDEN)

    request.scope['user_id'] = user.id
    auth.user = user
    return auth


# async def has_permission(request: Request, auth: Auth = Depends(get_current_user),
#                          required_permission: Optional[str] = None,
#                          check_data_scope: bool = False) -> Auth:
#     # 管理员不受数据范围和权限限制，可以操作所有的数据
#     is_admin = auth.user.id == 1
#     if is_admin:
#         return auth
#
#     # 如果没有传权限码，不做限制
#     if not required_permission:
#         return auth
#
#     permission = set()
#     for role in auth.user.roles:
#         for permi in role.permissions:
#             permission.add(permi.code)
#
#     if len(required_permission) != len(required_permission & permission):
#         raise AccessException(message='无权限操作')
#
#     return auth


class AuthPermission:
    def __init__(
        self, *, auto: bool = True, permissions_code: Optional[List[str]] = None, check_data_scope: bool = True
    ) -> None:
        self.auto = auto
        self.permissions_code = permissions_code
        self.check_data_scope = check_data_scope
        self._permission_cache = {}  # 添加内存缓存

    async def __call__(self, request: Request, auth: Auth = Depends(get_current_user)) -> Auth:
        """
        权限验证入口
        :param request: FastAPI 请求对象
        :param auth: 认证信息
        :return: Auth 对象
        """
        # todo 考虑在登录的时候把权限和角色缓存在Redis中， 从Redis中获取权限角色，减少DB的访问
        #  查询缓存如果为空则从数据库中取并缓存到Redis中
        if await self._is_super_admin(auth):
            return auth

        auth.check_data_scope = self.check_data_scope

        # 基于请求路径验证权限
        if self.auto:
            await self.verify_by_request_path(request, auth)
        # 基于权限码验证权限
        elif self.permissions_code:
            await self.verify_by_permission_code(auth)
        else:
            # 如果既不是自动验证也没有指定权限码，则拒绝访问
            raise AccessException(message='无权限操作')

        return auth

    @staticmethod
    async def _is_super_admin(auth: Auth) -> bool:
        """判断是否为超级管理员"""
        # 检查用户是否拥有管理员角色
        return auth.user.is_superuser

    @staticmethod
    async def verify_by_request_path(request: Request, auth: Auth) -> bool:
        """
        验证请求路径权限
        /v1/system/user
        /v1/system/user/{user_id}
        :param request: FastAPI请求对象
        :param auth: 认证信息
        :raises: AccessException 当用户无权限时抛出
        """
        path = request.scope['path']
        method = request.method

        # 从用户角色中获取权限路径集合
        user_permissions_path = {
            permission.path
            for role in auth.user.roles
            for permission in role.permissions
            if permission.method
            and permission.path
            and (permission.method == '*' or permission.method.lower() == method.lower())
        }

        # 检查路径权限匹配
        for p_path in user_permissions_path:
            try:
                # 处理通配符路径
                if '*' in p_path:
                    pattern = AuthPermission._build_wildcard_pattern(p_path)
                    if re.match(pattern, path):
                        return True
                # 精确匹配路径
                elif p_path == path:
                    return True
            except re.error:
                # 记录无效的路径模式
                tdump_log.warning('Invalid permission path pattern: %s', p_path)
                continue

        raise AccessException(message='无权限操作')

    @staticmethod
    def _build_wildcard_pattern(path: str) -> str:
        """
        构建通配符路径的正则表达式模式
        :param path: 包含通配符的路径
        :return: 正则表达式模式字符串
        """
        # 先转义所有特殊字符
        escaped_path = re.escape(path)

        # 然后处理通配符（注意顺序很重要）
        # 先处理 ** (匹配多级路径，包括/)
        pattern = escaped_path.replace('\\*\\*', '.*')
        # 再处理单个 * (匹配单级路径，不包括/)
        pattern = pattern.replace('\\*', '[^/]*')

        # 确保完全匹配
        return f'^{pattern}$'

    # @staticmethod
    # def verify_by_request_path(request: Request, auth: Auth) -> None:
    #     """
    #     基于请求路径和方法验证用户权限
    #     :param request: FastAPI请求对象
    #     :param auth: 认证信息
    #     :return: None
    #     """
    #     request_path = request.url.path
    #     request_method = request.method
    #
    #     # 收集用户所有权限路径和方法
    #     user_permissions = []
    #     for role in auth.user.roles:
    #         for permission in role.permissions:
    #             if permission.path and permission.method:
    #                 user_permissions.append((permission.path, permission.method))
    #
    #     # 检查是否有匹配的权限
    #     has_permission = False
    #     for perm_path, perm_method in user_permissions:
    #         # 检查HTTP方法是否匹配
    #         if perm_method != "*" and perm_method != request_method:
    #             continue
    #
    #         # 路径匹配逻辑
    #         if AuthPermission._match_path(perm_path, request_path):
    #             has_permission = True
    #             break
    #
    #     if not has_permission:
    #         raise AccessException(message='无权限操作')
    #
    # @staticmethod
    # def _match_path(permission_path: str, request_path: str) -> bool:
    #     """
    #     检查请求路径是否匹配权限路径
    #     支持通配符:
    #     - '*' 匹配单级路径
    #     - '**' 匹配多级路径
    #     """
    #     # 完全匹配
    #     if permission_path == request_path:
    #         return True
    #
    #     # 处理通配符
    #     if '*' in permission_path:
    #         # 将权限路径转换为正则表达式
    #         # 替换 ** 为 .* （多级匹配）
    #         pattern = permission_path.replace('**', '.*')
    #         # 替换 * 为 [^/]* （单级匹配）
    #         pattern = pattern.replace('*', '[^/]*')
    #         # 转义其他特殊字符
    #         pattern = re.escape(pattern).replace('\\[', '[').replace('\\]', ']').replace('\\.', '.')
    #         # 确保匹配整个路径
    #         pattern = f'^{pattern}$'
    #
    #         return bool(re.match(pattern, request_path))
    #
    #     return False

    async def verify_by_permission_code(self, auth: Auth) -> bool:
        """
        根据权限码验证用户权限
        :param auth: 认证信息
        :return: None
        :raises: AccessException 当用户无权限时抛出
        """
        user_id = auth.user.id
        cache_key = f'user_permissions_{user_id}'

        # 尝试从缓存获取用户权限码
        if cache_key in self._permission_cache:
            user_permission_codes = self._permission_cache[cache_key]
        else:
            # 从用户角色中获取所有权限码
            user_permission_codes = {
                permission.code for role in auth.user.roles for permission in role.permissions if permission.code
            }
            # 缓存权限码（实际项目中应该考虑缓存过期时间）
            self._permission_cache[cache_key] = user_permission_codes

        # 验证是否具有所有必需的权限码
        if not all(code in user_permission_codes for code in self.permissions_code):
            raise AccessException(message='无权限操作')

        return True
