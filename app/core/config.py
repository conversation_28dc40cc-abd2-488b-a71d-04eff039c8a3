# -*- coding: utf-8 -*-
import argparse
import os
from pathlib import Path
import sys
from typing import Dict, List, Literal, Optional, Tuple, Union

from dotenv import load_dotenv
from pydantic import computed_field
from pydantic_settings import BaseSettings
from sqlalchemy import URL


class AppSettings(BaseSettings):
    """
    APP 相关配置
    """

    # ================================================= #
    # ******************* APP基础配置 ****************** #
    # ================================================= #
    # 开发环境模式配置
    # APP_DEBUG: bool = True
    APP_ENV: str = 'dev'

    APP_DEMO: bool = False

    APP_RELOAD: bool = True

    # 项目根路径
    APP_BASE_DIR: Path = Path(__file__).parent.parent

    # 主机IP
    APP_SERVER_HOST: str = '0.0.0.0'
    # 主机端口
    APP_SERVER_PORT: int = 8000
    # API前缀
    APP_API_PREFIX: str = ''
    # 是否查询IP对应的地址
    APP_IP_LOCATION_QUERY: bool = True
    # 是否允许一个账号同时登录
    APP_SAME_TIME_LOGIN: bool = True

    # ================================================= #
    # ******************* API文档配置 ****************** #
    # ================================================= #
    # 项目文档
    APP_NAME: str = 'TdumpAdmin'
    APP_DESCRIPTION: Optional[str] = 'TdumpAdminAPI'
    APP_VERSION: str = '1.0'
    # 文档地址 默认为docs
    APP_DOCS_URL: str = f'{APP_API_PREFIX}/docs'
    # 文档关联请求数据接口
    APP_OPENAPI_URL: str = f'{APP_API_PREFIX}/openapi.json'
    # redoc 文档
    APP_REDOC_URL: Optional[str] = f'{APP_API_PREFIX}/redoc'

    # ================================================= #
    # ******************* 缓存目录配置 ****************** #
    # ================================================= #
    APP_CACHES_DIR: str = 'caches'
    APP_CACHES_PATH: Path = APP_BASE_DIR.joinpath(APP_CACHES_DIR)

    # ================================================= #
    # ***************** 静态文件目录配置 ***************** #
    # ================================================= #
    # 是否启用静态文件目录访问
    APP_STATIC_ENABLE: bool = True
    # 路由访问
    APP_STATIC_URL: str = '/static'
    # 静态文件目录名
    APP_STATIC_DIR: str = 'static'
    # 静态文件目录绝对路径
    APP_STATIC_ROOT: Path = APP_BASE_DIR.joinpath(APP_STATIC_DIR)

    # ================================================= #
    # ***************** 临时文件目录配置 ***************** #
    # ================================================= #
    # 是否启用临时文件目录访问
    APP_TEMP_ENABLE: bool = False
    # 路由访问
    APP_TEMP_URL: str = '/temp'
    # 临时文件目录名
    APP_TEMP_DIR: str = 'temp'
    # 临时文件目录绝对路径
    APP_TEMP_ROOT: Path = APP_BASE_DIR.joinpath(APP_TEMP_DIR)

    # ================================================= #
    # ********************* 日志配置 ******************* #
    # ================================================= #
    # # 是否开启保存每次请求日志到本地
    # APP_REQUEST_LOG_RECORD: bool = True
    # 是否开启每次操作日志记录到数据库
    APP_OPERATION_LOG_RECORD: bool = True
    # 只记录包括的请求方式记录到数据库
    APP_OPERATION_RECORD_METHOD: List[str] = ['POST', 'PUT', 'PATCH', 'DELETE']
    # 忽略的操作接口函数名称，列表中的函数名称不会被记录到操作日志中
    APP_IGNORE_OPERATION_FUNCTION: List[str] = ['get_captcha_for_login']

    # ================================================= #
    # ******************** 跨域配置 ******************** #
    # ================================================= #
    # 是否启用跨域
    APP_CORS_ORIGIN_ENABLE: bool = True
    # 只允许访问的域名列表, * 代表所有
    APP_ALLOW_ORIGINS: List[str] = ['*']
    # 允许跨域的http方法, 例如 get、post、put 等
    APP_ALLOW_METHODS: List[str] = ['*']
    # 允许携带的headers, 可以用来鉴别来源等
    APP_ALLOW_HEADERS: List[str] = ['*']
    # 是否支持携带 cookie
    APP_ALLOW_CREDENTIALS: bool = True

    # ================================================= #
    # ******************** 中间件配置 ******************* #
    # ================================================= #
    APP_MIDDLEWARE: List[Optional[Tuple[str,Dict]]] = [
        # 1. CORS中间件（最外层）
        ('app.core.middlewares.CustomCORSMiddleware', {}) if APP_CORS_ORIGIN_ENABLE else None,
        # 2. 全局异常处理中间件（第二层）
        ('app.core.middlewares.GlobalExceptionMiddleware', {'debug': APP_ENV == 'dev'}),
        # 3. 日志中间件（第三层）
        ('app.core.middlewares.LoggerMiddleware', {'ignore_url': ['/favicon.ico', 'websocket']}),

        # ('app.core.middlewares.GlobalExceptionMiddleware', {'debug': APP_ENV == 'dev'}),
        # ('app.core.middlewares.LoggerMiddleware', {'ignore_url': ['/favicon.ico', 'websocket']}),
        ('app.core.middlewares.DemoEnvMiddleware', {}) if APP_DEMO else None,
    ]

    @property
    def get_backend_app_attributes(self) -> Dict[str, Union[str, bool, None]]:
        """
        设置 `FastAPI` 自定义属性
        """
        return {
            'debug': True if self.APP_ENV == 'dev' else False,
            'title': self.APP_NAME,
            'version': self.APP_VERSION,
            'description': self.APP_DESCRIPTION,
            'docs_url': self.APP_DOCS_URL,
            'openapi_url': self.APP_OPENAPI_URL,
            'redoc_url': self.APP_REDOC_URL,
            'root_path': self.APP_API_PREFIX
        }

    @property
    def get_cors_middleware_attributes(self) -> Dict[str, Union[List[str], bool]]:
        """
        设置 `CORSMiddleware` 自定义属性
        """
        return {
            'allow_origins': self.APP_ALLOW_ORIGINS,
            'allow_methods': self.APP_ALLOW_METHODS,
            'allow_headers': self.APP_ALLOW_HEADERS,
            'allow_credentials': self.APP_ALLOW_CREDENTIALS
        }


class SecretSettings(BaseSettings):
    """
   安全相关配置
    """
    # ================================================= #
    # ******************* 登录认证配置 ****************** #
    # ================================================= #
    # 生产环境保管好 token的SECRET_KEY
    JWT_SECRET_KEY: str = 'aeq)s(*&(&)()WEQasd8**&^9asda_asdasd*&*&^+_sda'
    # 生成token的加密算法
    JWT_ALGORITHM: str = 'HS256'
    # access_token 过期时间
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24
    # refresh_token 过期时间
    JWT_REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7
    # jwt token 过期时间
    JWT_REDIS_EXPIRE_MINUTES: int = 30

    # ================================================= #
    # ******************** 验证码配置 ******************* #
    # ================================================= #
    # 是否开启登录验证码功能
    CAPTCHA_ENABLE: bool = True
    # 验证码过期时间
    CAPTCHA_EXPIRE_SECONDS: int = 60


class DataBaseSettings(BaseSettings):
    """
    # ================================================= #
    # ******************** 数据库配置 ******************* #
    # ================================================= #
    """
    # SQL_DB_URL: Union[PostgresDsn, MySQLDsn] = "postgresql+asyncpg://odoo:@127.0.0.1:5432/fastapi_vue_admin"
    # db 配置
    DIALECT_MAP: Dict = {
        'postgresql': {
            'dialect': 'postgres',
            'driver': {
                'async': 'postgresql+asyncpg',
                'synchronous': 'postgresql+psycopg2'
            }
        },
        'sqlite3': {
            'dialect': 'sqlite',
            'driver': {
                'async': 'sqlite+aiosqlite',
                'synchronous': 'sqlite'
            }
        },
        'oracle': {
            'dialect': 'oracle',
            'driver': {
                'async': None,
                'synchronous': 'oracle+cx_oracle'
            }
        },
        'mysql': {
            'dialect': 'mysql',
            'driver': {
                'async': 'mysql+aiomysql',
                'synchronous': 'mysql+pymysql'}
        },
        'mssql': {
            'dialect': 'tsql',
            'driver': {
                'async': 'mssql+aioodbc',
                'synchronous': 'mssql+pyodbc'
            }
        },
        # 可扩展更多
    }

    # DB_ENABLE: bool = True
    DB_TYPE: Literal['postgresql', 'sqlite3', 'oracle', 'mysql', 'mssql'] = 'postgresql'
    DB_USERNAME: str = 'odoo'
    DB_PASSWORD: str = 'Drag0n@190916'
    # DB_PASSWORD = parse.quote_plus(DB_PASSWORD)
    DB_HOST: str = 'localhost'
    DB_PORT: int = 5432
    DB_DATABASE: str = 'msfwx'
    # 数据库连接的可选参数
    # DB_QUERY: dict = {"sslmode": "disable"}
    DB_QUERY: dict = {}

    # SQLALCHEMY_DATABASE_URL:Union[PostgresDsn, MySQLDsn] = f"postgresql+asyncpg://{DB_USERNAME}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_DATABASE}"

    # 设置是否输出执行的SQL具体信息
    DB_ECHO: bool = True
    # 允许溢出连接池大小的最大连接数
    DB_MAX_OVERFLOW: int = 10
    # 连接池大小，0表示连接数无限制
    DB_POOL_SIZE: int = 50
    # 连接回收时间（单位：秒）
    DB_POOL_RECYCLE: int = 3600
    # 连接池中没有线程可用时，最多等待的时间（单位：秒）
    DB_POOL_TIMEOUT: int = 30

    @computed_field
    @property
    def sqlglot_parse_dialect(self) -> str:
        return self.DIALECT_MAP.get(self.DB_TYPE, {}).get('dialect', self.DB_TYPE)

    # @computed_field
    # @property
    def get_db_driver(self, synchronous=False) -> str:
        if synchronous:
            return self.DIALECT_MAP.get(self.DB_TYPE, {}).get('driver', {}).get('synchronous', None)
        return self.DIALECT_MAP.get(self.DB_TYPE, {}).get('driver', {}).get('async', None)

    # @computed_field
    # @property
    # def get_db_url(self, synchronous=False) -> str:
    #     driver = self.get_db_driver(synchronous)
    #     if not driver:
    #         raise ValidationError(f"Unsupported dialect: {self.DB_TYPE}")
    #     driver_name = driver['name']
    #     if self.DB_TYPE == 'sqlite3':
    #         return f"{driver_name}:///{self.DB_DATABASE}"
    #     return f"{driver_name}://{self.DB_USERNAME}:{quote_plus(self.DB_PASSWORD)}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_DATABASE}"

    def get_db_param(self):
        db_params = {
            'username': self.DB_USERNAME,
            'password': self.DB_PASSWORD,
            'host': self.DB_HOST,
            'port': self.DB_PORT,
            'database': self.DB_DATABASE,
            'query': self.DB_QUERY
        }
        return db_params

    def get_db_url(self, synchronous=False):
        db_url = URL(
            drivername=self.get_db_driver(synchronous=synchronous),
            **self.get_db_param()
        )
        return db_url

    def get_engine_params(self):
        params = {
            'echo': db_settings.DB_ECHO,
            'max_overflow': db_settings.DB_MAX_OVERFLOW,
            'pool_size': db_settings.DB_POOL_SIZE,
            'pool_recycle': db_settings.DB_POOL_RECYCLE,
            'pool_timeout': db_settings.DB_POOL_TIMEOUT,
            'future': True,
            'pool_pre_ping': True
        }
        return params


class RedisSettings(BaseSettings):
    """
    # ================================================= #
    # ******************** Redis配置 ******************* #
    # ================================================= #
    """
    REDIS_ENABLE: bool = True
    # REDIS_URL: RedisDsn = "redis://127.0.0.1:6379/0"
    REDIS_HOST: str = '127.0.0.1'
    REDIS_PORT: int = 6379
    REDIS_USERNAME: str = ''
    REDIS_PASSWORD: str = ''
    REDIS_DATABASE: int = 2


class GenSettings:
    """
    代码生成配置
    """

    # author = 'insistence'
    # package_name = 'module_admin.system'
    # auto_remove_pre = False
    # table_prefix = 'sys_'
    # allow_overwrite = False
    #
    # GEN_PATH = 'vf_admin/gen_path'
    #
    # def __init__(self):
    #     if not os.path.exists(self.GEN_PATH):
    #         os.makedirs(self.GEN_PATH)


class UploadAndDownloadSettings(BaseSettings):
    """
    # ================================================= #
    # ***************** 文件上传配置 ******************** #
    # ================================================= #
    """
    # 文件上传路径
    UPLOAD_PATH: str = '/static/upload'
    # 文件上传大小限制
    UPLOAD_SIZE: float = 5 * 1024 * 1024
    DEFAULT_ALLOWED_EXTENSION: List[str] = [
        # 图片
        'bmp',
        'gif',
        'jpg',
        'jpeg',
        'png',
        # word excel powerpoint
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'html',
        'htm',
        'txt',
        # 压缩文件
        'rar',
        'zip',
        'gz',
        'bz2',
        # 视频格式
        'mp4',
        'avi',
        'rmvb',
        # pdf
        'pdf',
    ]


class MinioSettings(UploadAndDownloadSettings):
    """
    minio 配置
    """


class Settings:
    """
    合并所有配置
    """

    def __init__(self):
        # 加载配置
        load_dotenv('.env')
        # self.parse_cli_args()

    @staticmethod
    def parse_cli_args():
        """
        解析命令行参数
        """
        if 'uvicorn' in sys.argv[0]:
            # 使用uvicorn启动时，命令行参数需要按照uvicorn的文档进行配置，无法自定义参数
            pass
        else:
            # 使用argparse定义命令行参数
            parser = argparse.ArgumentParser(description='命令行参数')
            parser.add_argument('--env', type=str, default='', help='运行环境')
            # 解析命令行参数
            args = parser.parse_args()
            # 设置环境变量，如果未设置命令行参数，默认APP_ENV为dev
            os.environ['APP_ENV'] = args.env if args.env else 'dev'
        # 读取运行环境
        run_env = os.environ.get('APP_ENV', '')
        # 运行环境未指定时默认加载.env.dev
        env_file = '.env.dev'
        # 运行环境不为空时按命令行参数加载对应.env文件
        if run_env != '':
            env_file = f'.env.{run_env}'

        # 加载配置
        load_dotenv(env_file)


    def get_app_config(self):
        return AppSettings()

    def get_secret_config(self):
        return SecretSettings()

    def get_database_config(self):
        return DataBaseSettings()


    def get_redis_config(self):
        return RedisSettings()

    def get_upload_config(self):
        return UploadAndDownloadSettings()

    def get_minio_config(self):
        return MinioSettings()

    def get_gen_config(self):
        return GenSettings()


# 实例化获取配置类
get_settings = Settings()
# 应用配置
app_settings = get_settings.get_app_config()
# Jwt配置
secret_settings = get_settings.get_secret_config()
# 数据库配置
db_settings = get_settings.get_database_config()
# Redis配置
redis_settings = get_settings.get_redis_config()
# 代码生成配置
code_gen_settings = get_settings.get_gen_config()
# 上传配置
upload_settings = get_settings.get_upload_config()
# minio配置
minio_settings = get_settings.get_minio_config()
