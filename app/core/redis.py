# -*- coding: utf-8 -*-

# aioredis内部使用了distutils，但是py 3.12已经移除了 distutils 模块
# import aioredis
# from aioredis import AuthenticationError, TimeoutError, RedisError
from fastapi import FastAPI
import redis.asyncio as redis
from redis.exceptions import AuthenticationError, RedisError, TimeoutError

from app.core.config import redis_settings
from app.core.exceptions.exception import TdumpException
from app.core.logger import tdump_log


class RedisUtil:
    """
    Redis
    """

    @staticmethod
    async def create_redis_poll() -> redis.Redis:
        tdump_log.info('连接Redis...')
        if not redis_settings.REDIS_ENABLE:
            raise TdumpException(message='请先配置Redis数据库链接并启用',
                                 desc='请启用 app/core/config.py: REDIS_ENABLE')

        redis_obj = await redis.from_url(
            url=f'redis://{redis_settings.REDIS_HOST}',
            port=redis_settings.REDIS_PORT,
            username=redis_settings.REDIS_USERNAME,
            password=redis_settings.REDIS_PASSWORD,
            db=redis_settings.REDIS_DATABASE,
            encoding='utf-8',
            decode_responses=True,
        )
        try:
            connection = await redis_obj.ping()
            if connection:
                tdump_log.info('Redis连接成功')
            else:
                tdump_log.error('Redis连接失败')
        except AuthenticationError as e:
            tdump_log.error(f'Redis用户名或密码错误。{e}')
        except TimeoutError as e:
            tdump_log.error(f'Redis连接超时。{e}')
        except RedisError as e:
            tdump_log.error(f'Redis连接错误。{e}')

        return redis_obj

    @staticmethod
    async def close_redis_pool(app: FastAPI):
        await app.state.redis.close()
        tdump_log.info('关闭Redis成功')

    @staticmethod
    async def init_sys_dict(redis):
        """
        初始化缓存数据
        :param redis:
        :return:
        """
        # async with AsyncSessionLocal() as session:
        #     await DictDataService.init_cache_sys_dict_services(session, redis)

    @staticmethod
    async def init_sys_config(redis):
        """
        初始化系统配置
        :param redis:
        :return:
        """
        # async with AsyncSessionLocal() as session:
        #     await ConfigService.init_cache_sys_config_services(session, redis)
