# -*- coding: utf-8 -*-
from enum import Enum

from fastapi import status


class ExceptionEnum(Enum):
    SUCCESS = ('200', '操作成功')
    FAILED = ('-1', '操作失败')
    BAD_REQUEST = ('400', '错误的请求')
    LIMITER = ('429', '访问的速度过快')
    PARAMETER = ('10031', '参数校验错误,请检查提交的参数信息')
    UNAUTHORIZED = ('10032', '未经许可授权')
    FORBIDDEN = ('10033', '失败！当前访问没有权限，或操作的数据没权限!')
    NOT_FOUND = ('10034', '访问对象不存在')
    METHOD_NOT_ALLOWED = ('10035', '不允许使用此方法提交访问')
    OTHER = ('10036', '未知的其他异常')
    INTERNAL_ERROR = ('10037', '程序员哥哥睡眠不足，系统崩溃了！')
    INVALID_TOKEN = ('10038', 'token无效')
    EXPIRED_TOKEN = ('10039', 'token过期')
    FILE_TOO_LARGE = ('10040', '文件体积过大')
    FILE_TOO_MANY = ('10041', '文件数量过多')
    FILE_EXTENSION = ('10041', '文件扩展名不符合规范')
    BUSINESS_ERROR = ('0000', '业务错误逻辑处理')
    LOGIN_ERROR = ('4001', '登录失败')

    # # 参数信息错误
    # PARAMETER_ERROR = ("10001", "参数处理异常错误")
    # FAILED = ("5000", "系统异常")
    # USER_NO_DATA = ("10001", "用户不存在")
    # USER_REGIESTER_ERROR = ("10002", "注册异常")
    # PERMISSIONS_ERROR = ("2000", "用户权限错误")


class TdumpException(Exception):
    __slots__ = ['message', 'code', 'status_code', 'desc']

    def __init__(
            self,
            code: str = status.HTTP_500_INTERNAL_SERVER_ERROR,
            message: str = None,
            status_code: int = status.HTTP_200_OK,
            desc: str = None
    ) -> None:
        self.message = message
        self.code = code
        self.status_code = status_code
        self.desc = desc


class NotFoundException(TdumpException):
    def __init__(self,
                 code: str = None,
                 message: str = None,
                 status_code: int = status.HTTP_404_NOT_FOUND,
                 desc: str = None):
        self.code = code or ExceptionEnum.NOT_FOUND.value[0]
        self.message = message or ExceptionEnum.NOT_FOUND.value[1]
        super(NotFoundException, self).__init__(self.code, self.message, status_code, desc)


class LoginException(TdumpException):
    def __init__(self,
                 code: str = None,
                 message: str = None,
                 status_code: int = status.HTTP_401_UNAUTHORIZED,
                 desc: str = None):
        self.code = code or ExceptionEnum.LOGIN_ERROR.value[0]
        self.message = message or ExceptionEnum.LOGIN_ERROR.value[1]
        super(LoginException, self).__init__(self.code, self.message, status_code, desc)


class AccessException(TdumpException):
    def __init__(self,
                 code: str = None,
                 message: str = None,
                 status_code: int = status.HTTP_401_UNAUTHORIZED,
                 desc: str = None):
        self.code = code or ExceptionEnum.UNAUTHORIZED.value[0]
        self.message = message or ExceptionEnum.UNAUTHORIZED.value[1]
        super(AccessException, self).__init__(self.code, self.message, status_code, desc)

class TokenAuthException(TdumpException):
    def __init__(self,
                 code: str = None,
                 message: str = None,
                 status_code: int = status.HTTP_401_UNAUTHORIZED,
                 desc: str = None):
        self.code = code or ExceptionEnum.INVALID_TOKEN.value[0]
        self.message = message or ExceptionEnum.INVALID_TOKEN.value[1]
        super(TokenAuthException, self).__init__(self.code, self.message, status_code, desc)


class TokenExpiredException(TdumpException):
    def __init__(self,
                 code: str = None,
                 message: str = None,
                 status_code: int = status.HTTP_401_UNAUTHORIZED,
                 desc: str = None):
        self.code = code or ExceptionEnum.EXPIRED_TOKEN.value[0]
        self.message = message or ExceptionEnum.EXPIRED_TOKEN.value[1]
        super(TokenExpiredException, self).__init__(self.code, self.message, status_code, desc)


class ParamsValidationException(TdumpException):
    def __init__(self,
                 code: str = None,
                 message: str = None,
                 status_code: int = status.HTTP_400_BAD_REQUEST,
                 desc: str = None):
        self.code = code or ExceptionEnum.PARAMETER.value[0]
        self.message = message or ExceptionEnum.PARAMETER.value[1]
        super(ParamsValidationException, self).__init__(self.code, self.message, status_code, desc)


class PermissionValidationException(TdumpException):
    def __init__(self,
                 code: str = None,
                 message: str = None,
                 status_code: int = status.HTTP_401_UNAUTHORIZED,
                 desc: str = None):
        self.code = code or ExceptionEnum.UNAUTHORIZED.value[0]
        self.message = message or ExceptionEnum.UNAUTHORIZED.value[1]
        super(PermissionValidationException, self).__init__(self.message, self.code, status_code, desc)
