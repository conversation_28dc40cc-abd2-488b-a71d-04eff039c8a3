# -*- coding: utf-8 -*-
import traceback

from fastapi import Request, status
from fastapi.exceptions import HTTPException, RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError as PydanticValidationError
from sqlalchemy.exc import InvalidRequestError, SQLAlchemyError

from app.core.exceptions.exception import (
    AccessException,
    LoginException,
    NotFoundException,
    ParamsValidationException,
    PermissionValidationException,
    TdumpException,
    TokenAuthException,
    TokenExpiredException,
)
from app.core.logger import tdump_log
from app.core.response import (
    BadRequestExceptionResp,
    ExpiredTokenExceptionResp,
    InternalErrorExceptionResp,
    InvalidTokenExceptionResp,
    LimiterResExceptionResp,
    LoginExceptionResp,
    MethodNotAllowedExceptionResp,
    NotFoundExceptionResp,
    ParameterExceptionResp,
    UnauthorizedExceptionResp,
)


class TdumpExceptionHandler:
    """
    将异常处理器放入一个类中，方便自动注入到FastAPI对象中， 也方便继承
    异常处理器也可以不放入到类中，不过得手工注册异常捕获器
    """

    @staticmethod
    async def custom_exception_handler(request: Request, exc: TdumpException) -> JSONResponse:
        """
        自定义异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return InternalErrorExceptionResp(message=exc.message, code=exc.code, status_code=exc.status_code)

    @staticmethod
    async def login_exception_handler(request: Request, exc: LoginException) -> JSONResponse:
        """
        登录异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return LoginExceptionResp(message=exc.message, code=exc.code, status_code=exc.status_code)

    @staticmethod
    async def access_exception_handler(request: Request, exc: AccessException) -> JSONResponse:
        """

        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return UnauthorizedExceptionResp(message=exc.message, code=exc.code, status_code=exc.status_code)

    @staticmethod
    async def token_auth_exception_handler(request: Request, exc: TokenAuthException) -> JSONResponse:
        """
        token 验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return InvalidTokenExceptionResp(message=exc.message, code=exc.code, status_code=exc.status_code)

    @staticmethod
    async def token_expired_exception_handler(request: Request, exc: TokenExpiredException) -> JSONResponse:
        """
        token 验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return ExpiredTokenExceptionResp(message=exc.message, code=exc.code, status_code=exc.status_code)

    @staticmethod
    async def params_validation_exception_handler(request: Request, exc: ParamsValidationException) -> JSONResponse:
        """
        token 验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return ParameterExceptionResp(message=exc.message, code=exc.code, status_code=exc.status_code)
    @staticmethod
    async def pydantic_validation_exception_handler(request: Request, exc: PydanticValidationError) -> JSONResponse:
        """
        Pydantic 验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.args}')
        traceback.print_exc()
        return ParameterExceptionResp(message='参数验证错误')

    @staticmethod
    async def permission_validation_exception_handler(request: Request,
                                                      exc: PermissionValidationException) -> JSONResponse:
        """
        token 验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return UnauthorizedExceptionResp(message=exc.message, code=exc.code, status_code=exc.status_code)

    @staticmethod
    async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
        """
        重写HTTPException异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.status_code}: {exc.detail}')

        if exc.status_code == status.HTTP_405_METHOD_NOT_ALLOWED:
            return MethodNotAllowedExceptionResp()
        if exc.status_code == status.HTTP_404_NOT_FOUND:
            return NotFoundExceptionResp()
        elif exc.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
            return LimiterResExceptionResp()
        elif exc.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR:
            return InternalErrorExceptionResp()
        elif exc.status_code == status.HTTP_400_BAD_REQUEST:
            return BadRequestExceptionResp(message=exc.detail)

        return InternalErrorExceptionResp(message=exc.detail, status_code=exc.status_code)

    @staticmethod
    async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
        """
        重写请求验证异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.errors()}')
        msg = exc.errors()[0].get('message')
        return ParameterExceptionResp(message=msg, status_code=status.HTTP_200_OK)

    @staticmethod
    async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
        """
        sqlalchemy异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.__str__()}')
        return InternalErrorExceptionResp(message='非法写入', code=5000, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @staticmethod
    async def sqlalchemy_invalid_request_error_handler(request: Request, exc: InvalidRequestError) -> JSONResponse:
        """
        sqlalchemy异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.__str__()}')
        return InternalErrorExceptionResp(message='非法写入', code=5000, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @staticmethod
    async def not_fund_exception_handler(request: Request, exc: NotFoundException) -> JSONResponse:
        """

        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.__str__()}')
        return NotFoundExceptionResp(message=exc.message, code=exc.code, status_code=exc.status_code)
