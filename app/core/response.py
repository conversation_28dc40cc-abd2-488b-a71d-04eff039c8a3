# -*- coding: utf-8 -*-
import json
import math
import time
from typing import Any, Dict, List, Optional, Tuple, Union

from fastapi import status
from fastapi.responses import JSONResponse, Response

from app.core.exceptions.exception import ExceptionEnum
from app.utils.json_helper import CsJsonEncoder


class ApiResponse(JSONResponse):
    """
    统一响应格式
    success: 是否成功
    status_code: 响应状态码
    code: 响应编码
    detail: 响应内容
    message: 响应消息提示
    """

    status_code: int = status.HTTP_200_OK
    code: Union[int, str] = 0
    message: str = '成功'
    success: bool = True
    timestamp: int = int(time.time() * 1000)
    detail: Optional[Dict[str, Any]] = None

    def __init__(
        self,
        *,
        success: bool = None,
        status_code: int = None,
        code: Union[int, str] = None,
        detail: Optional[Dict[str, Any]] = None,
        message: str = None,
        **kwargs,
    ):
        """

        :param success: 是否成功
        :param status_code: 响应状态码
        :param code: 响应编码
        :param detail: 响应内容
        :param message: 响应消息提示
        :param kwargs: 其他的参数
        """
        self.message = message or self.message
        self.code = code or self.code
        self.success = success or self.success
        self.status_code = status_code or self.status_code
        self.detail = detail or self.detail

        body = {
            'message': self.message,
            'code': self.code,
            'success': self.success,
            'detail': self.detail,
            'timestamp': self.timestamp,
        }
        super(ApiResponse, self).__init__(status_code=self.status_code, content=body, **kwargs)

    def render(self, content: Any) -> bytes:
        return json.dumps(
            content, ensure_ascii=False, allow_nan=False, indent=None, separators=(',', ':'), cls=CsJsonEncoder
        ).encode('utf-8')


class SuccessResp(ApiResponse):
    status_code: int = status.HTTP_200_OK
    code: Union[int, str] = ExceptionEnum.SUCCESS.value[0]
    message: str = ExceptionEnum.SUCCESS.value[1]
    success: bool = True


class FailResp(ApiResponse):
    status_code: int = status.HTTP_200_OK
    code: Union[int, str] = ExceptionEnum.FAILED.value[0]
    message: str = ExceptionEnum.FAILED.value[1]
    success: bool = False


class BadRequestExceptionResp(FailResp):
    status_code: int = status.HTTP_400_BAD_REQUEST
    code: Union[int, str] = ExceptionEnum.BAD_REQUEST.value[0]
    message: str = ExceptionEnum.BAD_REQUEST.value[1]


class LimiterResExceptionResp(FailResp):
    status_code: int = status.HTTP_429_TOO_MANY_REQUESTS
    code: Union[int, str] = ExceptionEnum.LIMITER.value[0]
    message: str = ExceptionEnum.LIMITER.value[1]


class ParameterExceptionResp(FailResp):
    status_code: int = status.HTTP_400_BAD_REQUEST
    code: Union[int, str] = ExceptionEnum.PARAMETER.value[0]
    message: str = ExceptionEnum.PARAMETER.value[1]


class UnauthorizedExceptionResp(FailResp):
    status_code: int = status.HTTP_401_UNAUTHORIZED
    code: Union[int, str] = ExceptionEnum.UNAUTHORIZED.value[0]
    message: str = ExceptionEnum.UNAUTHORIZED.value[1]


class ForbiddenExceptionResp(FailResp):
    status_code: int = status.HTTP_403_FORBIDDEN
    code: Union[int, str] = ExceptionEnum.FORBIDDEN.value[0]
    message: str = ExceptionEnum.FORBIDDEN.value[1]


class NotFoundExceptionResp(FailResp):
    status_code: int = status.HTTP_404_NOT_FOUND
    code: Union[int, str] = ExceptionEnum.NOT_FOUND.value[0]
    message: str = ExceptionEnum.NOT_FOUND.value[1]


class MethodNotAllowedExceptionResp(FailResp):
    status_code: int = status.HTTP_405_METHOD_NOT_ALLOWED
    code: Union[int, str] = ExceptionEnum.METHOD_NOT_ALLOWED.value[0]
    message: str = ExceptionEnum.METHOD_NOT_ALLOWED.value[1]


class OtherExceptionResp(FailResp):
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    code: Union[int, str] = ExceptionEnum.OTHER.value[0]
    message: str = ExceptionEnum.OTHER.value[1]


class InternalErrorExceptionResp(FailResp):
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    code: Union[int, str] = ExceptionEnum.INTERNAL_ERROR.value[0]
    message: str = ExceptionEnum.INTERNAL_ERROR.value[1]


class LoginExceptionResp(FailResp):
    status_code: int = status.HTTP_401_UNAUTHORIZED
    code: Union[int, str] = ExceptionEnum.LOGIN_ERROR.value[0]
    message: str = ExceptionEnum.LOGIN_ERROR.value[1]


class InvalidTokenExceptionResp(FailResp):
    status_code: int = status.HTTP_401_UNAUTHORIZED
    code: Union[int, str] = ExceptionEnum.INVALID_TOKEN.value[0]
    message: str = ExceptionEnum.INVALID_TOKEN.value[1]


class ExpiredTokenExceptionResp(FailResp):
    status_code: int = status.HTTP_422_UNPROCESSABLE_ENTITY
    code: Union[int, str] = ExceptionEnum.EXPIRED_TOKEN.value[0]
    message: str = ExceptionEnum.EXPIRED_TOKEN.value[1]


class FileTooLargeExceptionResp(FailResp):
    status_code: int = status.HTTP_413_REQUEST_ENTITY_TOO_LARGE
    code: Union[int, str] = ExceptionEnum.FILE_TOO_LARGE.value[0]
    message: str = ExceptionEnum.FILE_TOO_LARGE.value[1]


class FileTooManyExceptionResp(FailResp):
    status_code: int = status.HTTP_413_REQUEST_ENTITY_TOO_LARGE
    code: Union[int, str] = ExceptionEnum.FILE_TOO_MANY.value[0]
    message: str = ExceptionEnum.FILE_TOO_MANY.value[1]


class FileExtensionExceptionResp(FailResp):
    status_code: int = status.HTTP_401_UNAUTHORIZED
    code: Union[int, str] = ExceptionEnum.FILE_EXTENSION.value[0]
    message: str = ExceptionEnum.FILE_EXTENSION.value[1]


class BusinessErrorExceptionResp(FailResp):
    status_code: int = status.HTTP_200_OK
    code: Union[int, str] = ExceptionEnum.BUSINESS_ERROR.value[0]
    message: str = ExceptionEnum.BUSINESS_ERROR.value[1]


class PaginationResponse(Response):
    """
    分页响应
    """

    def __init__(
        self,
        data: List[Any] = None,
        msg: Optional[str] = 'success',
        page: int = 1,
        page_size: int = 10,
        code: int = status.HTTP_200_OK,
        status_code: int = status.HTTP_200_OK,
    ) -> None:
        if page < 1 or page_size < 1:
            raise

        total, data = self.get_paginated_response(data, page, page_size)
        has_next = True if math.ceil(total / page_size) > page else False
        self.data = {
            'code': code,
            'total': total,
            'page': page,
            'page_size': page_size,
            'has_next': has_next,
            'data': data,
            'message': msg,
        }
        super().__init__(content=self.data, status_code=status_code)

    @staticmethod
    def get_paginated_response(data, page, page_size) -> Tuple[int, List[Any]]:
        # 计算起始索引和结束索引
        start = (page - 1) * page_size
        end = page * page_size

        paginated_data = data[start:end]
        return len(data), paginated_data
