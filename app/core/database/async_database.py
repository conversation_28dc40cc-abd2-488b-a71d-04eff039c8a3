# -*- coding: utf-8 -*-

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from app.core.config import db_settings


#
# db_params = {
#     'username': db_settings.DB_USERNAME,
#     'password': db_settings.DB_PASSWORD,
#     'host': db_settings.DB_HOST,
#     'port': db_settings.DB_PORT,
#     'database': db_settings.DB_DATABASE,
#     'query': db_settings.DB_QUERY
# }
#
# db_url = URL(
#     drivername=db_settings.get_db_driver(),
#     **db_params
# )

async_engine = create_async_engine(url=db_settings.get_db_url(),
                                  **db_settings.get_engine_params()
                                   )
AsyncSessionLocal = async_sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=async_engine,
    expire_on_commit=False,
    class_=AsyncSession
)



# 使用asynccontextmanager装饰器可以使用with
@asynccontextmanager
async def async_get_db_session() -> AsyncGenerator[AsyncSession, None]:
    db_session = None
    try:
        db_session = AsyncSessionLocal()
        yield db_session
        await db_session.commit()
    except SQLAlchemyError as ex:
        await db_session.rollback()
        raise ex
    finally:
        await db_session.close()

