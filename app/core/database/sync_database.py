# -*- coding: utf-8 -*-
from contextlib import contextmanager

from sqlalchemy import create_engine
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import sessionmaker

from app.core.config import db_settings


sync_engine = create_engine(url=db_settings.get_db_url(synchronous=True),
                            **db_settings.get_engine_params()
                            )

# # 如果数据库不存在则自动创建
# try:
#     if not database_exists(sync_engine.url):
#         create_database(sync_engine.url)
# except Exception as e:
#     raise e

SyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine,
    expire_on_commit=False,
    future=False
)


# 需要使用这个来装饰一下，才可以使用with
@contextmanager
def sync_get_db_session():
    session = SyncSessionLocal()
    try:
        yield session
        session.commit()
    except SQLAlchemyError as ex:
        session.rollback()
        raise ex
    finally:
        session.close()
