2025-07-20 02:29:12.467 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 02:29:12.468 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 02:29:12.470 | thread_id:8512862272 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-07-20 02:29:43.688 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 02:29:43.688 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 02:29:43.691 | thread_id:8512862272 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-07-20 02:30:17.904 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 02:30:17.905 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 02:30:17.907 | thread_id:8512862272 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-07-20 02:30:17.908 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:10:24.405 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:10:24.405 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:10:24.407 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:10:24.408 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:24:51.800 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:24:51.804 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "UY9WFWKGTUFBYVv2GpR6aB", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:24:51"}}
2025-07-20 17:24:52.161 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/favicon.ico
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'image', 'referer': 'http://localhost:9099/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:24:52.162 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "3h6D56SRHaLUhjZ7x3Nxrd", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/favicon.ico", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:24:52"}}
2025-07-20 17:24:55.818 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:24:55.819 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "eucp845eL9S9QQ7kD2nz3g", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:24:55"}}
2025-07-20 17:25:00.078 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/v1
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:25:00.081 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "JYgjgaoukxSUQsF83tuCUM", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/v1", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:25:00"}}
2025-07-20 17:25:04.860 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/v1/system
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:25:04.861 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "47TxyNgbdZc85zFYJGKjR7", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/v1/system", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:25:04"}}
2025-07-20 17:25:11.638 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:25:11.639 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "cWczYo8iYWZi4Vry4AdTK2", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:25:11"}}
2025-07-20 17:25:11.655 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/static/swagger/swagger-ui/swagger-ui.css
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': 'text/css,*/*;q=0.1', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'style', 'referer': 'http://localhost:9099/api/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:25:11.658 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "65p2f6FcXqenVPsVp3tyFR", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/static/swagger/swagger-ui/swagger-ui.css", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:25:11"}}
2025-07-20 17:25:11.658 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/static/swagger/swagger-ui/swagger-ui-bundle.js
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'script', 'referer': 'http://localhost:9099/api/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:25:11.664 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "82Az3YqKyDLUUV6Dz9ms5X", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/static/swagger/swagger-ui/swagger-ui-bundle.js", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:25:11"}}
2025-07-20 17:25:11.731 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/api/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/api/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:25:11.732 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "3QZZBMxPNQ6e7HZbmr4z8n", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/api/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:25:11"}}
2025-07-20 17:25:11.769 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/static/swagger/favicon.png
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'image', 'referer': 'http://localhost:9099/api/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:25:11.771 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "5oXLBe5bqjy7N5igLFcLFN", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/static/swagger/favicon.png", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:25:11"}}
2025-07-20 17:26:45.156 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 17:26:47.466 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:26:47.466 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:26:47.469 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:26:47.469 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:26:47.473 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:26:47.475 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "d7VsxVqc7jXqoz6tyCpZJj", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:26:47"}}
2025-07-20 17:26:47.595 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/api/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/api/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:26:47.596 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "FgFKFQY9YFLDoP7tDnMwgh", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/api/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:26:47"}}
2025-07-20 17:26:55.361 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:26:55.361 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:26:55.363 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:26:55.364 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:27:02.049 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:27:02.055 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6RQC6hHFK7fMVJWaRgXycF", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:27:02"}}
2025-07-20 17:27:07.152 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:27:07.153 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "XqBgapzPNFc4dz4ZTGex8v", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:27:07"}}
2025-07-20 17:27:42.074 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/v1/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:27:42.076 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "ZzTWfuntznQQBfvgALPPAA", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/v1/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:27:42"}}
2025-07-20 17:28:00.360 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/v1/system
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:00.362 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "MZkbTPLM8gBf7XJgPXgMrj", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/v1/system", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:00"}}
2025-07-20 17:28:07.883 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/api/v1/system
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:07.884 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "28Ferpxi9NpbrrEcvwYg9b", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/api/v1/system", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:07"}}
2025-07-20 17:28:12.417 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/v1/system
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:12.419 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "gHVsdMFYJnKmseNbZPbuFf", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/v1/system", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:12"}}
2025-07-20 17:28:15.988 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/v1/system/1
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:15.990 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6Be7LzCZmKPgsNBpsR9wyT", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/v1/system/1", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:15"}}
2025-07-20 17:28:36.638 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/v1/system
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-purpose': 'prefetch;prerender', 'purpose': 'prefetch', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:36.640 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "Aq7hht9V9DJRteSNaAsfJH", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/v1/system", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:36"}}
2025-07-20 17:28:36.904 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/v1/system
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:36.905 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "kj3mu7hSebUxdZ5EapWidW", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/v1/system", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:36"}}
2025-07-20 17:28:45.303 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:45.304 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "XbARvFgHjK9icydUoXAKs3", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:45"}}
2025-07-20 17:28:59.297 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:59.298 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "WKtb8bJLjmjtHfsFAPD36N", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:59"}}
2025-07-20 17:28:59.363 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/api/api/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:28:59.364 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "jr9vbFaU3bbvt6V59gyR8L", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/api/api/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:28:59"}}
2025-07-20 17:29:23.105 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:29:23.105 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:29:23.107 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:29:23.108 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:29:26.476 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:29:26.478 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "bfgwEW2Gz4P5seSeRURnEm", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:29:26"}}
2025-07-20 17:29:26.552 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:29:26.554 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6ARzWNJnvviZziZhmMfk5n", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:29:26"}}
2025-07-20 17:29:33.158 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:29:33.160 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "nYp2TXTNojLmnLDB7DJjub", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:29:33"}}
2025-07-20 17:29:33.226 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:29:33.228 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "VL4Pi3gkW3GQECZCRKJ72s", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:29:33"}}
2025-07-20 17:32:58.159 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 17:38:13.418 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:38:13.418 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:38:13.420 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:38:13.421 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:38:18.562 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:38:18.565 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "kiWLD77EcvkgRe7RgSeXBW", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:38:18"}}
2025-07-20 17:38:18.648 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:38:18.650 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "VxiXh5z3tq8mN2QJBfZSTP", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:38:18"}}
2025-07-20 17:40:15.623 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 17:40:17.904 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:40:17.904 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:40:17.907 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:40:17.907 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:42:16.461 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 17:50:51.583 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:50:51.584 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:50:51.586 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:50:51.586 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:50:59.160 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:50:59.163 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "RprNSZ6aKMuYhyzhq74Xg2", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:50:59"}}
2025-07-20 17:50:59.261 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:50:59.262 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "SRaFjZ2Sc8PV7PeFi5ANKy", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:50:59"}}
2025-07-20 17:51:00.565 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:51:00.567 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "kLvSEZGPuEv4uP8erFNqMX", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:51:00"}}
2025-07-20 17:51:00.636 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:51:00.637 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "LGegmUwCbVLzaCjrAwHKjx", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:51:00"}}
2025-07-20 17:51:01.200 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:51:01.201 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "RRD7vvB2ZUueUU7JYqrNHf", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:51:01"}}
2025-07-20 17:51:01.251 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:51:01.252 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "cQDRjSwvysgshQWE9dUzyx", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:51:01"}}
2025-07-20 17:51:01.521 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:51:01.524 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "Tuv5iMqfysTJexKHshnisF", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:51:01"}}
2025-07-20 17:51:01.569 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:51:01.573 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "AmGUHLCfdXjDMN7Yn9GDzZ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:51:01"}}
2025-07-20 17:51:01.762 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:51:01.764 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "2BgYxbGQegu8Bbz6VV3CKG", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:51:01"}}
2025-07-20 17:51:01.815 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:51:01.816 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "GCo55kGhcX6qb9JvGcWy6z", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:51:01"}}
2025-07-20 17:52:33.938 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 17:52:36.280 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:52:36.280 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:52:36.282 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:52:36.283 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:52:36.287 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:52:36.289 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "AVx6sXqRBf9R2MsngmjEze", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:52:36"}}
2025-07-20 17:52:36.382 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:52:36.384 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "SQ2SqCXxfipnodXo8JFJCu", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:52:36"}}
2025-07-20 17:52:47.223 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:52:47.228 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "FgLMvfdxdFLimvNbeyvgcy", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:52:47"}}
2025-07-20 17:52:49.924 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 17:52:49.924 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 17:52:49.929 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 17:52:49.929 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 17:52:49.933 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:52:49.935 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "nG5nAWNE5uKNVpnxMmDPkX", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:52:49"}}
2025-07-20 17:53:04.114 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 17:53:04.130 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "MgpHVYVobT5gpzKvF9fwpE", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 17:53:04"}}
2025-07-20 20:43:59.838 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:43:59.847 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "J8c7FkfZFoCaQcj92BWftr", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:43:59"}}
2025-07-20 20:44:53.329 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 20:44:53.330 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 20:44:53.334 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 20:44:53.334 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 20:44:55.872 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:44:55.874 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "STfWr78jaUYk369CLReoJZ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:44:55"}}
2025-07-20 20:46:19.776 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:46:19.781 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "CqKHY7YCrxDzGsaUYiLFH4", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:46:19"}}
2025-07-20 20:46:20.093 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:46:20.094 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "a23dM333YSVDWVEdcXrRSU", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:46:20"}}
2025-07-20 20:47:09.311 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 20:48:17.713 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 20:48:17.714 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 20:48:17.716 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 20:48:17.716 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 20:48:20.062 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:48:20.065 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "F4X8zzHizDBHTpMvmiLoDt", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:48:20"}}
2025-07-20 20:48:20.158 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:48:20.162 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "hyN928mGGt9yJURB5NM56n", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:48:20"}}
2025-07-20 20:48:21.295 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:48:21.297 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "64KQd5waDPpxruaRS9nSA8", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:48:21"}}
2025-07-20 20:48:21.359 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:48:21.360 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "dYi5sZzxfN3eXXDgy7n2kr", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:48:21"}}
2025-07-20 20:49:27.688 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 20:49:31.250 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 20:49:31.250 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 20:49:31.255 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 20:49:31.256 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 20:50:53.853 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 20:50:56.637 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 20:50:56.637 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 20:50:56.639 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 20:50:56.639 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 20:51:38.616 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:51:38.621 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "jHhjV42WXfSW6qAkfoWLwk", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:51:38"}}
2025-07-20 20:51:38.705 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:51:38.712 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "RNzQ6zMhktt9vXmfRq7eQQ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:51:38"}}
2025-07-20 20:52:04.330 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'user-agent': 'curl/8.9.1', 'accept': '*/*'})
IP:127.0.0.1
2025-07-20 20:52:04.333 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "YAkyZAt2kH84Gx4xGT9pyF", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:52:04"}}
2025-07-20 20:52:44.231 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 20:52:48.027 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 20:52:48.027 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 20:52:48.029 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 20:52:48.029 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 20:53:04.556 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:53:04.565 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "DBhj5CRjX7T9fwTasDCzL8", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:53:04"}}
2025-07-20 20:53:04.654 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:53:04.657 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "JnPEqxboSHenDuS5WxXpAP", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:53:04"}}
2025-07-20 20:55:02.917 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 20:55:02.917 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 20:55:02.921 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 20:55:02.922 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 20:55:03.988 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:55:03.990 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "CGU4LrZSbUjkptHEEaVk3F", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:55:03"}}
2025-07-20 20:55:04.092 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:55:04.095 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "hrYhTQThZsuEdZX6YgDYj8", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:55:04"}}
2025-07-20 20:56:30.695 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:56:30.697 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "AkssyU86MZ577frtMMQtQi", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:56:30"}}
2025-07-20 20:56:34.262 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:56:34.273 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "StqPMVowKNjTW9tEfpxyoV", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:56:34"}}
2025-07-20 20:57:05.434 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:57:05.444 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "cz6Luxh9mGPKSxDi8Qia3U", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:57:05"}}
2025-07-20 20:58:14.263 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 20:58:20.697 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 20:58:20.697 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 20:58:20.699 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 20:58:20.700 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 20:58:22.055 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 20:58:22.057 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "TjisZCwK7LfKPhhvmLubjD", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "//v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 20:58:22"}}
2025-07-20 20:59:12.464 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 20:59:14.856 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 20:59:14.856 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 20:59:14.858 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 20:59:14.858 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 20:59:56.007 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 21:01:41.899 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:01:41.901 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:01:41.908 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:01:41.908 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:01:47.571 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 21:01:57.118 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:01:57.119 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:01:57.124 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:01:57.124 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:02:01.984 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099//v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:03:32.085 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:03:32.085 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:03:32.089 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:03:32.089 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:03:32.152 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:03:48.333 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "G5iuRjpVuEgneTN8btULG7", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:03:48"}}
2025-07-20 21:03:59.552 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:04:11.252 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "m6ig4rC3tBdyd5Z8aXWAV8", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:04:11"}}
2025-07-20 21:04:11.472 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/123/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:04:12.905 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "5MJe5pzaRCAkxnC6gzq2Pr", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/123/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:04:12"}}
2025-07-20 21:04:23.104 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/123/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:04:26.869 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "8r4yJz9DubMpNeufnpo76F", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/123/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:04:26"}}
2025-07-20 21:04:34.819 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-purpose': 'prefetch;prerender', 'purpose': 'prefetch', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:04:39.945 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "Mb4HMpMV9s3osvZ8cvzsbJ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:04:39"}}
2025-07-20 21:04:40.156 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/123/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-purpose': 'prefetch;prerender', 'sec-ch-ua-mobile': '?0', 'purpose': 'prefetch', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:05:19.148 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:05:19.149 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:05:19.154 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:05:19.154 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:05:23.049 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:05:31.292 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "innZEBzmyNUPxDZo8QLzbT", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:05:31"}}
2025-07-20 21:05:46.859 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 21:05:56.395 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:05:56.396 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:05:56.407 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:05:56.408 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:05:59.223 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:06:09.942 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "9HDR43oQV4JNCwNiB4hq8j", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:06:09"}}
2025-07-20 21:06:56.451 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 21:07:05.230 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:07:05.230 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:07:05.236 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:07:05.236 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:07:13.942 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:07:16.422 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "8f4sNaiQAfcJEDaznZkV7t", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:07:16"}}
2025-07-20 21:07:53.699 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 21:08:20.953 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:08:20.954 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:08:20.956 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:08:20.957 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:08:28.313 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:08:28.316 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "8gqAbzmqE5NG5ys8VjioE9", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:08:28"}}
2025-07-20 21:08:34.016 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-purpose': 'prefetch;prerender', 'purpose': 'prefetch', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:08:34.018 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "CXgYxTwF5BS6K2dJyLohgF", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:08:34"}}
2025-07-20 21:08:34.196 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-purpose': 'prefetch;prerender', 'sec-ch-ua-mobile': '?0', 'purpose': 'prefetch', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:08:34.199 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "aMB8ymwNjwLm7pnwmhDEiM", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:08:34"}}
2025-07-20 21:08:42.401 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/123/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:08:42.403 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "c7YmPgBuq8SijPrYr2gB5D", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/123/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:08:42"}}
2025-07-20 21:09:57.354 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:09:57.355 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:09:57.357 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:09:57.357 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:10:03.608 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-purpose': 'prefetch;prerender', 'purpose': 'prefetch', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:03.610 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6ViTWfgotMbVYcVhiXbA76", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:03"}}
2025-07-20 21:10:03.784 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-purpose': 'prefetch;prerender', 'sec-ch-ua-mobile': '?0', 'purpose': 'prefetch', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:03.784 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "J7TRP5b65zNJXveePvHytj", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:03"}}
2025-07-20 21:10:16.263 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 21:10:21.061 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 21:10:21.062 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 21:10:21.064 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 21:10:21.064 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 21:10:23.161 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:23.164 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "NQuTaVRZbynHoPf4i8Fe4o", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:23"}}
2025-07-20 21:10:23.221 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:23.222 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "ecsPxaqNqGGXPz2RdYf8nz", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:23"}}
2025-07-20 21:10:27.350 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:27.351 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "Hc9EP3CPq9tbUrofa9oaJX", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:27"}}
2025-07-20 21:10:27.418 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:27.422 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "AaPponAoU5kracUAQWuoTu", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:27"}}
2025-07-20 21:10:27.917 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:27.919 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "5EwNodU7yiNWt5hxcycYRC", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:27"}}
2025-07-20 21:10:27.976 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:27.977 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "gFtLQYkj6E8Et776uRQyWw", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:27"}}
2025-07-20 21:10:28.110 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:28.111 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "Ghjn2m9efnEDXPLCz7wYdB", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:28"}}
2025-07-20 21:10:28.154 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:28.156 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "nRknWRggPXkjTZMoJfHPVy", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:28"}}
2025-07-20 21:10:28.322 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:28.325 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "C88DA6B8eFvwwTjKxrTgWw", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:28"}}
2025-07-20 21:10:28.374 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:28.375 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "QvrGRQqsbiAKt2mToGHb4M", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:28"}}
2025-07-20 21:10:28.517 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:28.521 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "NdcUhyB4iEQtwKdKDvcPih", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:28"}}
2025-07-20 21:10:28.567 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:28.568 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "BjWKzjFprvsvUkhdkptPja", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:28"}}
2025-07-20 21:10:33.172 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 21:10:33.174 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "nNefoWemLYVrgzfrv4ysiS", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 21:10:33"}}
2025-07-20 21:28:53.974 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-20 23:22:53.123 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:26 - tdumpAdmin启动中...
2025-07-20 23:22:53.123 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-20 23:22:53.128 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-20 23:22:53.128 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动中...
2025-07-20 23:22:58.276 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 23:22:58.280 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "kRnab3mtVdCTtNEeAmzCHS", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 23:22:58"}}
2025-07-20 23:22:58.814 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:118 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=733e1872bbaba6836362bb79e714f39a7ceab25f'})
IP:127.0.0.1
2025-07-20 23:22:58.817 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "HLZWhoRo98aYvVPhWc3mib", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-20 23:22:58"}}
2025-07-20 23:23:29.940 | thread_id:8512862272 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
