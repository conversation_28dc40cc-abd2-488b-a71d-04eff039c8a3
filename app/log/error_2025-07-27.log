2025-07-27 16:10:25.529 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-07-27 16:41:09.422 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:42:27.180 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:42:58.592 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResPartnerModel(res_partner)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:49:38.444 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - CFTJVRmm84J8RXYsHvG23M：索引：1:日志信息写入异常
2025-07-27 16:49:56.436 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - b4ySZS3auLS7wVeeCzNsmV：索引：1:日志信息写入异常
2025-07-27 16:49:56.473 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:51:42.967 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - KjCP7hBot4mkJ2c7ZkN7nD：索引：1:日志信息写入异常
2025-07-27 16:51:42.993 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:54:17.917 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - MQVQo9WYac8GAgJbmD7jq4：索引：1:日志信息写入异常
2025-07-27 16:54:17.949 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Error creating backref 'partner' on relationship 'ResPartnerModel.user_ids': property of that name exists on mapper 'Mapper[ResUserModel(res_user)]'
2025-07-27 16:54:54.832 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 7J8nsABDek3oe2xqVwLoPq：索引：1:日志信息写入异常
2025-07-27 16:54:54.869 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:55:39.390 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - LQiPVYgE7M2sYF3bnCAthb：索引：1:日志信息写入异常
2025-07-27 16:55:39.421 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Error creating backref 'partner' on relationship 'ResPartnerModel.user_ids': property of that name exists on mapper 'Mapper[ResUserModel(res_user)]'
2025-07-27 17:01:12.090 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - kG95wMeppUzS8t4USpbuJW：索引：1:日志信息写入异常
2025-07-27 17:01:12.131 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:05:38.325 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - BXoJw52hbVowzPAyp3eFrH：索引：1:日志信息写入异常
2025-07-27 17:05:38.360 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,When initializing mapper Mapper[ResPartnerModel(res_partner)], expression '[user_id]' failed to locate a name ("name 'user_id' is not defined"). If this is a class name, consider adding this relationship() to the <class 'app.models.entity.system.users.ResPartnerModel'> class after both dependent classes have been defined.
2025-07-27 17:06:38.944 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - M5Rnxayk9BQqibbcxXXCp2：索引：1:日志信息写入异常
2025-07-27 17:08:05.558 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - jzoZcTLgNLDwtYwBnFPzw7：索引：1:日志信息写入异常
2025-07-27 17:08:05.599 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:09:32.564 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ZTEX5XHhH9qnWr3SkCwodN：索引：1:日志信息写入异常
2025-07-27 17:09:41.126 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:10:21.755 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ACAiCbGNhgSvHnGJP5fLZh：索引：1:日志信息写入异常
2025-07-27 17:15:10.843 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResPartnerModel(res_partner)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResPartnerModel.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:15:27.805 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - A36p54mZc3s9yurTAJboNt：索引：1:日志信息写入异常
2025-07-27 17:22:15.412 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:22:15.433 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - iDKWSTE253zCXUo6emcvTa：索引：1:日志信息写入异常
2025-07-27 17:22:15.436 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - jPyC5QtC4nyFCG9sL46byq：索引：1:日志信息写入异常
2025-07-27 17:22:16.428 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:22:17.093 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:22:29.703 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - QsvWE2WeFw2yXYd6WKvvAe：索引：1:日志信息写入异常
2025-07-27 17:22:34.377 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:28:20.070 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - fJBpbsFtweitodWLPG4tMg：索引：1:日志信息写入异常
2025-07-27 17:28:25.395 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:29:46.468 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - XfSZQYRRnaQJjxHdMkYCp3：索引：1:日志信息写入异常
2025-07-27 17:29:48.588 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.roles - there are multiple foreign key paths linking the tables via secondary table 'res_user_roles'.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference from the secondary table to each of the parent and child tables.
2025-07-27 17:34:11.443 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VsKAASfqNTgcAv86TvWtJb：索引：1:日志信息写入异常
2025-07-27 17:34:13.677 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,When initializing mapper Mapper[ResRoleModel(res_role)], expression 'ResDeptModel' failed to locate a name ('ResDeptModel'). If this is a class name, consider adding this relationship() to the <class 'app.models.entity.system.users.ResRoleModel'> class after both dependent classes have been defined.
2025-07-27 17:36:12.935 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VWyxwvRuKwEYG25yNBCyS6：索引：1:日志信息写入异常
2025-07-27 17:36:15.312 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,When initializing mapper Mapper[ResRoleModel(res_role)], expression 'ResDeptModel' failed to locate a name ('ResDeptModel'). If this is a class name, consider adding this relationship() to the <class 'app.models.entity.system.users.ResRoleModel'> class after both dependent classes have been defined.
2025-07-27 17:38:17.148 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - m7HoHNdMVKcS28bzSUAwvc：索引：1:日志信息写入异常
2025-07-27 17:38:21.379 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,relationship 'users' expects a class or a mapper argument (received: <class 'sqlalchemy.sql.schema.Table'>)
2025-07-27 17:38:41.952 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 3E4ameKHcyLVXVPSTgBnZN：索引：1:日志信息写入异常
2025-07-27 17:40:52.431 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResPositionModel(res_position)]'. Original exception was: relationship 'users' expects a class or a mapper argument (received: <class 'sqlalchemy.sql.schema.Table'>)
2025-07-27 17:41:01.664 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - Bd4ZVtupXbMNbkMZ8qkEoj：索引：1:日志信息写入异常
2025-07-27 17:45:24.719 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - A7GW4BHurvFZuJAUgMRrK8：索引：1:日志信息写入异常
2025-07-27 17:47:02.720 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 3bvtb5mXJ6oGEsCeVYWEfo：索引：1:日志信息写入异常
2025-07-27 17:48:01.372 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - Mp7x2SzAuSwmMBWxwXtkMw：索引：1:日志信息写入异常
2025-07-27 17:49:41.126 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - dxwRVuap3xNU3X4fSydLur：索引：1:日志信息写入异常
2025-07-27 18:10:38.510 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 6w42KxqqoxNjLxVoP4yZDX：索引：1:日志信息写入异常
2025-07-27 18:14:09.617 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - nENw9TpDrkdJ6sY8DDkYsT：索引：1:日志信息写入异常
2025-07-27 18:14:57.025 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - MjbjM8JKNtFXv9vFQ3L9AV：索引：1:日志信息写入异常
2025-07-27 18:16:34.644 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 7QrFgSMYjq7d9thEKxJA2E：索引：1:日志信息写入异常
2025-07-27 18:17:01.216 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - GQtGz3APkFkFBVctkd6rVa：索引：1:日志信息写入异常
2025-07-27 18:18:01.157 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - KTnnh9Jux3FWyWuvKcwUC5：索引：1:日志信息写入异常
2025-07-27 18:19:04.050 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - QiKrthphDDG2vMj9Eypfsq：索引：1:日志信息写入异常
2025-07-27 18:23:58.519 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - K7mZ6msPAsdpXFcQDpgiEo：索引：1:日志信息写入异常
2025-07-27 18:24:36.682 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - S8cBmxHcEEijZKcVtuu42y：索引：1:日志信息写入异常
2025-07-27 18:25:23.433 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - mmc5vQVKeAedTc9P3Qkg8z：索引：1:日志信息写入异常
2025-07-27 18:25:53.237 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - k4QNRQU7u2DeXMYmCb8p9u：索引：1:日志信息写入异常
2025-07-27 18:26:53.393 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - MzZGpway2MZY6KXzJmrTCx：索引：1:日志信息写入异常
2025-07-27 18:27:23.821 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - JrJ6ZRazBkjKVF4DdQuHLS：索引：1:日志信息写入异常
2025-07-27 18:29:30.961 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ZnFtJLgatvQBDNRZy55fNJ：索引：1:日志信息写入异常
2025-07-27 18:31:38.847 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - bFzDyFGTJvea6SbcvB3wpz：索引：1:日志信息写入异常
2025-07-27 18:32:16.782 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - M8PyzAomafpJNrLSJyZMLP：索引：1:日志信息写入异常
2025-07-27 18:33:48.959 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VDdDW8SxrQLUySYyMyfKBs：索引：1:日志信息写入异常
2025-07-27 22:36:40.351 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - MTCU7fS62HGYck8q5xMGiF：索引：1:日志信息写入异常
2025-07-27 22:40:15.181 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/user/info/current,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 22:47:20.805 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/user/info/current,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
