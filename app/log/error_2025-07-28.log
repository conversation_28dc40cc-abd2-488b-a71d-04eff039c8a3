2025-07-28 00:11:33.806 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - XhawSRdwU6FzAvTfMug7R7：索引：1:日志信息写入异常
2025-07-28 00:11:33.827 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:11:38.659 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:61 - 请求地址：http://localhost:9099/v1/system/user/info/current,token无效,None
2025-07-28 00:11:43.031 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:61 - 请求地址：http://localhost:9099/v1/system/user/info/current,token无效,None
2025-07-28 00:12:26.097 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VVVohMNAfj6hMPTbU6JSrq：索引：1:日志信息写入异常
2025-07-28 00:12:26.103 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:13:18.703 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - QeYfzSQhPdY9vJdnBP2gX4：索引：1:日志信息写入异常
2025-07-28 00:13:26.423 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:17:10.821 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - CLBsoYV57yw7wwGCFwzwGW：索引：1:日志信息写入异常
2025-07-28 00:17:11.922 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:17:36.875 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - CAkfqZffuSFpq5znRfg7oH：索引：1:日志信息写入异常
2025-07-28 00:17:43.866 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:21:23.485 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - M2ecnwuwJWkoDS7gsYmwbR：索引：1:日志信息写入异常
2025-07-28 00:21:28.933 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:24:32.298 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:31:13.412 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.company and back-reference ResCompanyModel.users are both of the same direction <RelationshipDirection.ONETOMANY: 1>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:34:39.926 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.company and back-reference ResCompanyModel.users are both of the same direction <RelationshipDirection.ONETOMANY: 1>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:36:22.133 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,For many-to-one relationship ResDepartmentsModel.parent, delete-orphan cascade is normally configured only on the "one" side of a one-to-many relationship, and not on the "many" side of a many-to-one or many-to-many relationship.  To force this relationship to allow a particular "ResDepartmentsModel" object to be referenced by only a single "ResDepartmentsModel" object at a time via the ResDepartmentsModel.parent relationship, which would allow delete-orphan cascade to take place in this direction, set the single_parent=True flag. (Background on this error at: https://sqlalche.me/e/20/bbf0)
2025-07-28 00:36:41.193 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResDepartmentsModel(res_department)]'. Original exception was: For many-to-one relationship ResDepartmentsModel.parent, delete-orphan cascade is normally configured only on the "one" side of a one-to-many relationship, and not on the "many" side of a many-to-one or many-to-many relationship.  To force this relationship to allow a particular "ResDepartmentsModel" object to be referenced by only a single "ResDepartmentsModel" object at a time via the ResDepartmentsModel.parent relationship, which would allow delete-orphan cascade to take place in this direction, set the single_parent=True flag. (Background on this error at: https://sqlalche.me/e/20/bbf0)
2025-07-28 00:39:44.268 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,When initializing mapper Mapper[ResPositionModel(res_position)], expression 'res_employee_positions_rel' failed to locate a name ("name 'res_employee_positions_rel' is not defined"). If this is a class name, consider adding this relationship() to the <class 'app.models.entity.system.system.ResPositionModel'> class after both dependent classes have been defined.
2025-07-28 00:42:00.099 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:47:03.737 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:47:50.724 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:49:56.263 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:50:37.670 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:51:17.592 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:53:41.473 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResEmployeeModel.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:57:45.742 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResEmployeeModel.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 01:56:28.865 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 02:02:32.039 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/user/info/current,greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-28 02:06:03.782 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/user/info/current,greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-28 10:09:57.452 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:94 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 10:13:30.079 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 10:14:02.000 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 10:34:56.017 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 11:37:08.057 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 11:40:24.508 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 11:52:20.852 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 13:37:36.245 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - 全局异常
GETURL:http://localhost:9099/v1/system/user/info/current
Headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 151, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/exceptions/handle.py", line 28, in catch_exceptions_middleware
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 295, in __call__
    raise e
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 292, in __call__
    response = await self.app(scope, receive, send)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/api/system/user.py", line 17, in get_current_user_info
    1 / 0
    ~~^~~
ZeroDivisionError: division by zero

2025-07-28 13:38:27.859 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - 全局异常
GETURL:http://localhost:9099/v1/system/user/info/current
Headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 151, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/exceptions/handle.py", line 28, in catch_exceptions_middleware
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 295, in __call__
    raise e
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 292, in __call__
    response = await self.app(scope, receive, send)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/api/system/user.py", line 17, in get_current_user_info
    1 / 0
    ~~^~~
ZeroDivisionError: division by zero

2025-07-28 13:46:27.575 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - 全局异常
GETURL:http://localhost:9099/v1/system/user/info/current
Headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 151, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/exceptions/handle.py", line 28, in catch_exceptions_middleware
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 163, in dispatch
    raise e
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 139, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/api/system/user.py", line 17, in get_current_user_info
    1 / 0
    ~~^~~
ZeroDivisionError: division by zero

2025-07-28 13:52:41.913 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - traceid:c3gWrVM4gmVkT6kZJqLwCB,捕获全局异常：
GETURL:http://localhost:9099/v1/system/user/info/current
Headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
2025-07-28 13:56:06.483 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - traceid:2cH8kNiktwmnEN7AMfsGsL,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
2025-07-28 13:56:36.310 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:31 - traceid:c2x9mUpVMUTYn9f6NjvLJW,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
2025-07-28 13:56:42.665 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:31 - traceid:fT4Awic5zAxa6EvSNdLVcU,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
2025-07-28 13:58:52.766 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:31 - traceid:DHN4ufznMgnYAGkyPp78fA,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
2025-07-28 14:15:50.295 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.global_exception_middleware:dispatch:29 - traceid:hsyWWc7BEr3mttcKgnRUoy,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
Exception: ZeroDivisionError: division by zero
