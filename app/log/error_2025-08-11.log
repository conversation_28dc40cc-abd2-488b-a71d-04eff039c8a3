2025-08-11 21:16:15.593 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 21:59:54.533 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。<PERSON>rro<PERSON> 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:00:27.028 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。<PERSON>rror 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:00:36.509 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。<PERSON><PERSON><PERSON> 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:11:51.888 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:11:54.532 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:12:05.088 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:12:15.876 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:13:40.068 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:14:14.486 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - grK3KRB7z3M4drH3qZm4H9：索引：1:日志信息写入异常
2025-08-11 22:14:14.492 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - grK3KRB7z3M4drH3qZm4H9：索引：2:日志信息写入异常
2025-08-11 22:14:14.520 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - BwZ5CpFgoNnujDaLiH7oj4：索引：1:日志信息写入异常
2025-08-11 22:14:14.526 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - jBwbjkE6CHzqGSu8VJGWy6：索引：1:日志信息写入异常
2025-08-11 22:14:14.534 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - BwZ5CpFgoNnujDaLiH7oj4：索引：2:日志信息写入异常
2025-08-11 22:14:14.535 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - jBwbjkE6CHzqGSu8VJGWy6：索引：2:日志信息写入异常
2025-08-11 22:14:14.729 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - UyoQJeT4L7f4BbpLKjVAL9：索引：1:日志信息写入异常
2025-08-11 22:14:14.823 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - UyoQJeT4L7f4BbpLKjVAL9：索引：2:日志信息写入异常
2025-08-11 22:14:27.926 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - QyxeU3F6W8kbaUhMDXFGmB：索引：1:日志信息写入异常
2025-08-11 22:14:27.929 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:14:27.930 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - QyxeU3F6W8kbaUhMDXFGmB：索引：2:日志信息写入异常
2025-08-11 22:15:51.189 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:16:53.252 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:17:06.159 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:17:17.365 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user?page=1&page_size=100&order_by=created_date&login=string&nickname=string&phone=string&email=string&active=0,token无效,None
2025-08-11 22:17:39.362 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user?page=1&page_size=100&order_by=created_date&login=string&nickname=string&phone=string&email=string&active=0,token无效,None
2025-08-11 22:18:32.922 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user?page=1&page_size=100&order_by=created_date&login=string&nickname=string&phone=string&email=string&active=0,token无效,None
2025-08-11 22:19:31.459 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user/current/info,token无效,None
2025-08-11 22:20:07.704 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user/current/info,token无效,None
2025-08-11 22:20:46.476 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user/current/info,token无效,None
2025-08-11 22:21:01.658 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:22:15.527 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:22:24.236 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:23:51.559 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:24:02.198 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user/current/info,token无效,None
2025-08-11 22:24:46.190 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:26:00.009 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:26:39.208 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:26:48.083 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:29:51.840 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:31:01.804 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:32:17.074 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:33:34.108 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:33:37.306 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:33:57.537 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:34:47.354 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login,Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:37:25.044 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:37:27.328 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:45:33.671 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:custom_exception_handler:46 - 请求地址：http://localhost:9099/v1/system/auth/login: 验证码不能为空: None
2025-08-11 22:45:53.414 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login: Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:49:05.236 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:custom_exception_handler:46 - 请求地址：http://localhost:9099/v1/system/auth/login: 验证码不能为空: None
2025-08-11 22:49:40.080 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login: Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:50:08.787 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResRoleModel(res_role)]'. Original exception was: Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:50:47.301 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResRoleModel(res_role)]'. Original exception was: Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:53:32.091 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:161 - 请求地址：http://localhost:9099/v1/system/auth/login: Executable SQL or text() construct expected, got None.
2025-08-11 22:53:58.796 | thread_id:8457265344 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:161 - 请求地址：http://localhost:9099/v1/system/auth/login: Executable SQL or text() construct expected, got None.
