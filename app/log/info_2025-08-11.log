2025-08-11 21:16:15.591 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 21:16:15.591 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 21:16:15.593 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 21:16:15.593 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:31 - tdumpAdmin启动成功...
2025-08-11 21:59:54.530 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 21:59:54.531 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 21:59:54.533 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 21:59:54.533 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:00:27.025 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:00:27.026 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:00:27.028 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:00:27.028 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:00:36.506 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:00:36.507 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:00:36.509 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:00:36.509 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:11:51.885 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:11:51.886 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:11:51.888 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:11:51.888 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:11:52.699 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:11:54.529 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:11:54.530 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:11:54.532 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:11:54.532 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:11:58.070 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:12:05.085 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:12:05.086 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:12:05.088 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:12:05.088 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:12:11.223 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:12:11.223 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:12:11.226 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:12:11.226 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:12:15.873 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:12:15.874 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:12:15.876 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:12:15.876 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:13:40.066 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:13:40.067 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:13:40.068 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:13:40.069 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:14:14.486 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - grK3KRB7z3M4drH3qZm4H9：索引：1:日志信息写入异常
2025-08-11 22:14:14.492 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - grK3KRB7z3M4drH3qZm4H9：索引：2:日志信息写入异常
2025-08-11 22:14:14.520 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - BwZ5CpFgoNnujDaLiH7oj4：索引：1:日志信息写入异常
2025-08-11 22:14:14.526 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - jBwbjkE6CHzqGSu8VJGWy6：索引：1:日志信息写入异常
2025-08-11 22:14:14.534 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - BwZ5CpFgoNnujDaLiH7oj4：索引：2:日志信息写入异常
2025-08-11 22:14:14.535 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - jBwbjkE6CHzqGSu8VJGWy6：索引：2:日志信息写入异常
2025-08-11 22:14:14.729 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - UyoQJeT4L7f4BbpLKjVAL9：索引：1:日志信息写入异常
2025-08-11 22:14:14.823 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - UyoQJeT4L7f4BbpLKjVAL9：索引：2:日志信息写入异常
2025-08-11 22:14:27.926 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - QyxeU3F6W8kbaUhMDXFGmB：索引：1:日志信息写入异常
2025-08-11 22:14:27.929 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:14:27.930 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:98 - QyxeU3F6W8kbaUhMDXFGmB：索引：2:日志信息写入异常
2025-08-11 22:15:48.183 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:15:51.187 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:15:51.188 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:15:51.189 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:15:51.190 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:16:49.862 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:16:53.250 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:16:53.251 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:16:53.252 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:16:53.252 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:16:53.258 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "F3XGa9KFEX3zfe6xRNRPv4", "trace_index": 1, "event_type": "request", "msg": {"url": "/docs", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "cache-control": "max-age=0", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"macOS\"", "upgrade-insecure-requests": "1", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "sec-fetch-site": "same-origin", "sec-fetch-mode": "navigate", "sec-fetch-user": "?1", "sec-fetch-dest": "document", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "content-type": null, "tz": null, "referer": null, "params": {}, "ts": "2025-08-11 22:16:53"}}
2025-08-11 22:16:53.258 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "F3XGa9KFEX3zfe6xRNRPv4", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.002s"}}
2025-08-11 22:16:53.502 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "idkZx5xxQpT27D35jRs9t7", "trace_index": 1, "event_type": "request", "msg": {"url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json,*/*", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json,*/*", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {}, "ts": "2025-08-11 22:16:53"}}
2025-08-11 22:16:53.737 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "idkZx5xxQpT27D35jRs9t7", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.236s"}}
2025-08-11 22:17:06.153 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "BeVsgaJ7MFniT8FTzJ8FRG", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "50", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Basic Og==", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:17:06"}}
2025-08-11 22:17:06.159 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:17:06.160 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "BeVsgaJ7MFniT8FTzJ8FRG", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.009s"}}
2025-08-11 22:17:17.363 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "dX8Xv4uK7piRvbDF9Qm684", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer undefined", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {"query_params": {"page": ["1"], "page_size": ["100"], "order_by": ["created_date"], "login": ["string"], "nickname": ["string"], "phone": ["string"], "email": ["string"], "active": ["0"]}}, "ts": "2025-08-11 22:17:17"}}
2025-08-11 22:17:17.365 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user?page=1&page_size=100&order_by=created_date&login=string&nickname=string&phone=string&email=string&active=0,token无效,None
2025-08-11 22:17:17.366 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "dX8Xv4uK7piRvbDF9Qm684", "trace_index": 2, "event_type": "response", "msg": {"status_code": 401, "process_time": "0.003s"}}
2025-08-11 22:17:39.353 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "VhP9teeniE8RdEQ3ejj2V5", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer undefined", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {"query_params": {"page": ["1"], "page_size": ["100"], "order_by": ["created_date"], "login": ["string"], "nickname": ["string"], "phone": ["string"], "email": ["string"], "active": ["0"]}}, "ts": "2025-08-11 22:17:39"}}
2025-08-11 22:17:39.362 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user?page=1&page_size=100&order_by=created_date&login=string&nickname=string&phone=string&email=string&active=0,token无效,None
2025-08-11 22:17:39.365 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "VhP9teeniE8RdEQ3ejj2V5", "trace_index": 2, "event_type": "response", "msg": {"status_code": 401, "process_time": "0.016s"}}
2025-08-11 22:18:06.421 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "a4LePi3PV2LzZrPhiczazB", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer undefined", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {"query_params": {"page": ["1"], "page_size": ["100"], "order_by": ["created_date"], "login": ["string"], "nickname": ["string"], "phone": ["string"], "email": ["string"], "active": ["0"]}}, "ts": "2025-08-11 22:18:06"}}
2025-08-11 22:18:32.922 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user?page=1&page_size=100&order_by=created_date&login=string&nickname=string&phone=string&email=string&active=0,token无效,None
2025-08-11 22:18:32.932 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "a4LePi3PV2LzZrPhiczazB", "trace_index": 2, "event_type": "response", "msg": {"status_code": 401, "process_time": "26.512s"}}
2025-08-11 22:19:13.200 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "mrK2Y9CSKtsWndu4x3pB7D", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user/current/info", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer undefined", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {}, "ts": "2025-08-11 22:19:13"}}
2025-08-11 22:19:31.459 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user/current/info,token无效,None
2025-08-11 22:19:31.463 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "mrK2Y9CSKtsWndu4x3pB7D", "trace_index": 2, "event_type": "response", "msg": {"status_code": 401, "process_time": "18.264s"}}
2025-08-11 22:19:53.573 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "T4mwt3PxnEANchwkGzA4Rz", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user/current/info", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer undefined", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {}, "ts": "2025-08-11 22:19:53"}}
2025-08-11 22:20:07.704 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user/current/info,token无效,None
2025-08-11 22:20:07.713 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "T4mwt3PxnEANchwkGzA4Rz", "trace_index": 2, "event_type": "response", "msg": {"status_code": 401, "process_time": "14.141s"}}
2025-08-11 22:20:41.587 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "mxFanSonuYoF8aZxJTfyXC", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user/current/info", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer undefined", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {}, "ts": "2025-08-11 22:20:41"}}
2025-08-11 22:20:46.476 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user/current/info,token无效,None
2025-08-11 22:20:46.478 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "mxFanSonuYoF8aZxJTfyXC", "trace_index": 2, "event_type": "response", "msg": {"status_code": 401, "process_time": "4.891s"}}
2025-08-11 22:21:01.655 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "iNqQMgRw3XhzST5NKnjZvf", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "50", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Basic Og==", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:21:01"}}
2025-08-11 22:21:01.658 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:21:01.659 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "iNqQMgRw3XhzST5NKnjZvf", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.004s"}}
2025-08-11 22:22:05.107 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:22:15.525 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:22:15.526 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:22:15.527 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:22:15.527 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:22:24.233 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "e97E8DKgkEPiYgStTzymmM", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "50", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Basic Og==", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:22:24"}}
2025-08-11 22:22:24.236 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:22:24.237 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "e97E8DKgkEPiYgStTzymmM", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.006s"}}
2025-08-11 22:23:51.547 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "KhN6SM93M9esfvvJsW6pBo", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "50", "sec-ch-ua-platform": "\"macOS\"", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "content-type": "application/x-www-form-urlencoded", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:23:51"}}
2025-08-11 22:23:51.559 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:23:51.560 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "KhN6SM93M9esfvvJsW6pBo", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.014s"}}
2025-08-11 22:24:02.195 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "6YEr4WaVQvpsihSTzmouN6", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user/current/info", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer undefined", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {}, "ts": "2025-08-11 22:24:02"}}
2025-08-11 22:24:02.198 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:79 - 请求地址：http://localhost:9099/v1/system/user/current/info,token无效,None
2025-08-11 22:24:02.199 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "6YEr4WaVQvpsihSTzmouN6", "trace_index": 2, "event_type": "response", "msg": {"status_code": 401, "process_time": "0.005s"}}
2025-08-11 22:24:46.182 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "H9WuBRmEM5SZ6nawHAN9kn", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "52", "sec-ch-ua-platform": "\"macOS\"", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "content-type": "application/x-www-form-urlencoded", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"username": "admin", "password": "123456", "captcha_key": "", "captcha": ""}}, "ts": "2025-08-11 22:24:46"}}
2025-08-11 22:24:46.190 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:24:46.191 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "H9WuBRmEM5SZ6nawHAN9kn", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.011s"}}
2025-08-11 22:25:45.668 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "YdJRatiVCtTgnwbLsjm4Yi", "trace_index": 1, "event_type": "request", "msg": {"url": "/docs", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "cache-control": "max-age=0", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"macOS\"", "upgrade-insecure-requests": "1", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "sec-fetch-site": "same-origin", "sec-fetch-mode": "navigate", "sec-fetch-user": "?1", "sec-fetch-dest": "document", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "content-type": null, "tz": null, "referer": null, "params": {}, "ts": "2025-08-11 22:25:45"}}
2025-08-11 22:25:45.670 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "YdJRatiVCtTgnwbLsjm4Yi", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.002s"}}
2025-08-11 22:25:45.867 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "CnSx2dH2m5YZwhbQf4tu4f", "trace_index": 1, "event_type": "request", "msg": {"url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json,*/*", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json,*/*", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {}, "ts": "2025-08-11 22:25:45"}}
2025-08-11 22:25:46.004 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "CnSx2dH2m5YZwhbQf4tu4f", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.138s"}}
2025-08-11 22:25:59.997 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "5JqzDHucDLW3TDggrdqfXK", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "50", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Basic Og==", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:25:59"}}
2025-08-11 22:26:00.009 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:26:00.010 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "5JqzDHucDLW3TDggrdqfXK", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.015s"}}
2025-08-11 22:26:36.149 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:26:39.207 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:26:39.207 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:26:39.208 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-08-11 22:26:39.209 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:26:48.081 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "PFKLrcyheFedpw8S7FMMQL", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "50", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Basic Og==", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:26:48"}}
2025-08-11 22:26:48.083 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:26:48.084 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "PFKLrcyheFedpw8S7FMMQL", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.004s"}}
2025-08-11 22:29:51.836 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "2uEXJuLg6rWsxtbCSbs4eV", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "50", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Basic Og==", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:29:51"}}
2025-08-11 22:29:51.840 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:29:51.841 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "2uEXJuLg6rWsxtbCSbs4eV", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.006s"}}
2025-08-11 22:31:01.799 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "iMLbdwA9dvamDiYfdKkXM7", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "user-agent": "curl/8.9.1", "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "content-length": "30"}, "user-agent": {"os": "Other ", "browser": "curl 8.9.1", "device": {"family": "Other", "brand": null, "model": null}}, "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": null, "params": {"from": {"username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:31:01"}}
2025-08-11 22:31:01.804 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:31:01.805 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "iMLbdwA9dvamDiYfdKkXM7", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.007s"}}
2025-08-11 22:32:17.071 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "QAbAMHaxHtiGTRJK293aPa", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "user-agent": "curl/8.9.1", "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "content-length": "30"}, "user-agent": {"os": "Other ", "browser": "curl 8.9.1", "device": {"family": "Other", "brand": null, "model": null}}, "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": null, "params": {"from": {"username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:32:17"}}
2025-08-11 22:32:17.074 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:32:17.074 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "QAbAMHaxHtiGTRJK293aPa", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.004s"}}
2025-08-11 22:33:26.809 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:33:33.245 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:33:33.245 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:33:33.252 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:33:33.252 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:33:34.104 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "S5WVNQBegBajNDLN2GBBDm", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "user-agent": "curl/8.9.1", "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "content-length": "30"}, "user-agent": {"os": "Other ", "browser": "curl 8.9.1", "device": {"family": "Other", "brand": null, "model": null}}, "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": null, "params": {"from": {"username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:33:34"}}
2025-08-11 22:33:34.108 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:33:34.109 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "S5WVNQBegBajNDLN2GBBDm", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.006s"}}
2025-08-11 22:33:37.304 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "Cp8cvrNZkoGwKtyFvjPZci", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "user-agent": "curl/8.9.1", "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "content-length": "30"}, "user-agent": {"os": "Other ", "browser": "curl 8.9.1", "device": {"family": "Other", "brand": null, "model": null}}, "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": null, "params": {"from": {"username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:33:37"}}
2025-08-11 22:33:37.306 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:33:37.307 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "Cp8cvrNZkoGwKtyFvjPZci", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.003s"}}
2025-08-11 22:33:46.344 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "XgCHm8bG7dEfKwacx5gbAx", "trace_index": 1, "event_type": "request", "msg": {"url": "/docs", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "cache-control": "max-age=0", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"macOS\"", "upgrade-insecure-requests": "1", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "sec-fetch-site": "same-origin", "sec-fetch-mode": "navigate", "sec-fetch-user": "?1", "sec-fetch-dest": "document", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "content-type": null, "tz": null, "referer": null, "params": {}, "ts": "2025-08-11 22:33:46"}}
2025-08-11 22:33:46.345 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "XgCHm8bG7dEfKwacx5gbAx", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.002s"}}
2025-08-11 22:33:46.451 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "RaAZeemExA3uEHQi4kTkDL", "trace_index": 1, "event_type": "request", "msg": {"url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json,*/*", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json,*/*", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "params": {}, "ts": "2025-08-11 22:33:46"}}
2025-08-11 22:33:46.643 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "RaAZeemExA3uEHQi4kTkDL", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.193s"}}
2025-08-11 22:33:57.535 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "UyPiUUPfYQzKPh8xAGZ95B", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "connection": "keep-alive", "content-length": "50", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Basic Og==", "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"", "sec-ch-ua-mobile": "?0", "x-requested-with": "XMLHttpRequest", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "origin": "http://localhost:9099", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=4a4598e85e1340dc3d0b13b70e887f29a1dd2ccb"}, "user-agent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:33:57"}}
2025-08-11 22:33:57.537 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login,[{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:33:57.538 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "UyPiUUPfYQzKPh8xAGZ95B", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.003s"}}
2025-08-11 22:34:34.967 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:34:37.932 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:34:37.932 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:34:37.938 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:34:37.938 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:34:47.354 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login,Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:35:52.354 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:35:52.355 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:35:52.361 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:35:52.361 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:36:03.207 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:36:06.312 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:36:06.313 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:36:06.317 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:36:06.317 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:36:50.305 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:36:50.305 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:36:50.318 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:36:50.318 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:37:20.480 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:37:24.512 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:37:24.512 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:37:24.517 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:37:24.517 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:37:25.042 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "85fbFJobCyiNBkMKwD6sSx", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "user-agent": "curl/8.9.1", "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "content-length": "30"}, "user-agent": {"os": "Other ", "browser": "curl 8.9.1", "device": {"family": "Other", "brand": null, "model": null}}, "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": null, "params": {"from": {"username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:37:25"}}
2025-08-11 22:37:25.044 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:37:25.045 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "85fbFJobCyiNBkMKwD6sSx", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.007s"}}
2025-08-11 22:37:27.327 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "DUwikcuK8LUisY4wMTRhti", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "headers": {"host": "localhost:9099", "user-agent": "curl/8.9.1", "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "content-length": "30"}, "user-agent": {"os": "Other ", "browser": "curl 8.9.1", "device": {"family": "Other", "brand": null, "model": null}}, "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": null, "params": {"from": {"username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:37:27"}}
2025-08-11 22:37:27.328 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:validation_exception_handler:152 - 请求地址：http://localhost:9099/v1/system/auth/login: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None}]
2025-08-11 22:37:27.329 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "DUwikcuK8LUisY4wMTRhti", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.003s"}}
2025-08-11 22:45:26.349 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:45:33.458 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:45:33.458 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:45:33.463 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:45:33.464 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:45:33.669 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "7dBvoFkV24XWmVKgqsCo9g", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": null, "user-agent": {"ua-string": "curl/8.9.1", "os": "Other ", "browser": "curl 8.9.1", "device": {"family": "Other", "brand": null, "model": null}}, "params": {"from": {"username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:45:33"}}
2025-08-11 22:45:33.671 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:custom_exception_handler:46 - 请求地址：http://localhost:9099/v1/system/auth/login: 验证码不能为空: None
2025-08-11 22:45:45.432 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "LjkQBJZUhbn7tfrvcTyyQE", "trace_index": 1, "event_type": "request", "msg": {"url": "/docs", "method": "GET", "ip": "127.0.0.1", "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "content-type": null, "tz": null, "referer": null, "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {}, "ts": "2025-08-11 22:45:45"}}
2025-08-11 22:45:45.713 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "bb8c4oha5E8F3qP3oWKqcP", "trace_index": 1, "event_type": "request", "msg": {"url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "accept": "application/json,*/*", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {}, "ts": "2025-08-11 22:45:45"}}
2025-08-11 22:45:53.377 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "V4VHiZGkGoHzuKeY2yY8sN", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:45:53"}}
2025-08-11 22:45:53.414 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login: Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:46:00.393 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "mcyHB77FK8oCjZQuX4VtyY", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "GET", "ip": "127.0.0.1", "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "content-type": null, "tz": null, "referer": null, "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {}, "ts": "2025-08-11 22:46:00"}}
2025-08-11 22:49:05.227 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "CVnhSTxDTRukLZjmENHZbd", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "*/*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": null, "user-agent": {"ua-string": "curl/8.9.1", "os": "Other ", "browser": "curl 8.9.1", "device": {"family": "Other", "brand": null, "model": null}}, "params": {"from": {"username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:49:05"}}
2025-08-11 22:49:05.236 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:custom_exception_handler:46 - 请求地址：http://localhost:9099/v1/system/auth/login: 验证码不能为空: None
2025-08-11 22:49:17.649 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:49:24.392 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:49:24.392 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:49:24.395 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:49:24.396 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:49:24.419 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "3PAiSgUWPM2b7n3MYnc2gW", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:49:24"}}
2025-08-11 22:49:40.080 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login: Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:50:03.232 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "4oXi3KKynjrwKWCvHA7KQ8", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:50:03"}}
2025-08-11 22:50:08.787 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResRoleModel(res_role)]'. Original exception was: Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:50:25.844 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "nLC9rTNV57KJ5fi8V2Ux6s", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:50:25"}}
2025-08-11 22:50:47.301 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_error_handler:169 - 请求地址：http://localhost:9099/v1/system/auth/login: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResRoleModel(res_role)]'. Original exception was: Mapper 'Mapper[ResPermissionModel(res_permission)]' has no property 'roles'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-11 22:51:43.136 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:51:47.294 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:51:47.295 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:51:47.301 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:51:47.302 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:52:04.189 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:52:07.198 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:52:07.199 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:52:07.205 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:52:07.205 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:52:53.934 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-08-11 22:53:00.162 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:53:00.162 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:53:00.168 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:53:00.168 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:53:09.942 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "4KLoKbqsGDjaUxbLQihxrE", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:53:09"}}
2025-08-11 22:53:32.091 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:161 - 请求地址：http://localhost:9099/v1/system/auth/login: Executable SQL or text() construct expected, got None.
2025-08-11 22:53:45.175 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "nz2oyCN7e3jYQ2U7P4pFM7", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:53:45"}}
2025-08-11 22:53:58.796 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:161 - 请求地址：http://localhost:9099/v1/system/auth/login: Executable SQL or text() construct expected, got None.
2025-08-11 22:54:13.046 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:54:13.046 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:54:13.050 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:54:13.050 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:54:13.055 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "5rsXJ68XjxJz8Q5Bs4ZUQn", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:54:13"}}
2025-08-11 22:54:16.745 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "fDnUYZzFZ36obkCqxWLao3", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:54:16"}}
2025-08-11 22:55:26.868 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:19 - tdumpAdmin启动中...
2025-08-11 22:55:26.868 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-08-11 22:55:26.873 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-08-11 22:55:26.873 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:42 - tdumpAdmin启动成功...
2025-08-11 22:55:26.879 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "kXhufJqmFc2ZGTDr8W3v9j", "trace_index": 1, "event_type": "request", "msg": {"url": "/docs", "method": "GET", "ip": "127.0.0.1", "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "content-type": null, "tz": null, "referer": null, "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {}, "ts": "2025-08-11 22:55:26"}}
2025-08-11 22:55:27.124 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "ZvCCESnvsCWyVY7yFuuiiq", "trace_index": 1, "event_type": "request", "msg": {"url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "accept": "application/json,*/*", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {}, "ts": "2025-08-11 22:55:27"}}
2025-08-11 22:55:35.049 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "gfJrnZDcaZe4Rthmvcp8LM", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/auth/login", "method": "POST", "ip": "127.0.0.1", "accept": "application/json, text/plain, */*", "content-type": "application/x-www-form-urlencoded", "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {"from": {"grant_type": "password", "username": "admin", "password": "123456"}}, "ts": "2025-08-11 22:55:35"}}
2025-08-11 22:55:44.483 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "4ffk5NeBUBD6XoJmjB35HD", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user/1", "method": "GET", "ip": "127.0.0.1", "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {}, "ts": "2025-08-11 22:55:44"}}
2025-08-11 22:55:55.434 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "HDXddb4gb45JRPKHDA252X", "trace_index": 1, "event_type": "request", "msg": {"url": "/v1/system/user/1", "method": "GET", "ip": "127.0.0.1", "accept": "application/json", "content-type": null, "tz": null, "referer": "http://localhost:9099/docs", "user-agent": {"ua-string": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X 10.15.7", "browser": "Chrome 139.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "params": {}, "ts": "2025-08-11 22:55:55"}}
