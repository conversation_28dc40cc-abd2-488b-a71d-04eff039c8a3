2025-07-27 16:10:25.527 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:10:25.527 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:10:25.529 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.redis:create_redis_poll:47 - Redis连接错误。Error 61 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-07-27 16:10:25.529 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:10:39.479 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:10:45.552 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:10:45.552 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:10:45.558 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:10:45.558 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:11:32.891 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/auth/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:11:32.895 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "fA8o3mqWmgo9FjrtgFmZP7", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 16:11:32"}}
2025-07-27 16:11:33.289 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/favicon.ico
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'image', 'referer': 'http://localhost:9099/v1/system/auth/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:11:33.291 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "SGSjmxCTKtEAhd97YBVSjk", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/favicon.ico", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 16:11:33"}}
2025-07-27 16:11:38.131 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:11:38.132 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6VUXYXCK6uXtoXra6gEZk2", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 16:11:38"}}
2025-07-27 16:11:43.646 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-purpose': 'prefetch;prerender', 'purpose': 'prefetch', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:11:43.649 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "iB8j6vx8gBgDVEQuMHmzzZ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 16:11:43"}}
2025-07-27 16:11:43.660 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/static/swagger/swagger-ui/swagger-ui.css
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-purpose': 'prefetch;prerender', 'sec-ch-ua-mobile': '?0', 'accept': 'text/css,*/*;q=0.1', 'purpose': 'prefetch', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'style', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:11:43.674 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/static/swagger/swagger-ui/swagger-ui-bundle.js
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-purpose': 'prefetch;prerender', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'purpose': 'prefetch', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'script', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:11:43.678 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "Q89dZiFP564tMkuox47qHL", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/static/swagger/swagger-ui/swagger-ui.css", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 16:11:43"}}
2025-07-27 16:11:43.681 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "c6NGGAcxYrDZj8FBZbVTcX", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/static/swagger/swagger-ui/swagger-ui-bundle.js", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 16:11:43"}}
2025-07-27 16:11:43.869 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-purpose': 'prefetch;prerender', 'sec-ch-ua-mobile': '?0', 'purpose': 'prefetch', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:11:43.870 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "auGwQ4WfcqeysAaMtrpXDC", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 16:11:43"}}
2025-07-27 16:11:56.187 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:11:56.190 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - GBRG6cZUrr9Jmqr5UYdckk：索引：1:日志信息写入异常
2025-07-27 16:12:40.369 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:12:40.372 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 8YQo665oLr3vjAPGXRbSdF：索引：1:日志信息写入异常
2025-07-27 16:13:24.844 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:13:24.844 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:13:24.852 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:13:24.852 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:13:26.159 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:13:26.162 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - KP98ZZn53dw428XAaTbY4Z：索引：1:日志信息写入异常
2025-07-27 16:16:00.542 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:16:07.068 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:16:07.069 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:16:07.075 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:16:07.075 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:16:09.984 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:16:09.986 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - N83HdYzBr5bNoyhbfTyw6S：索引：1:日志信息写入异常
2025-07-27 16:18:16.499 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:18:16.503 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - NLpSUMZH7i3aSHnhjJYa35：索引：1:日志信息写入异常
2025-07-27 16:19:28.631 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:19:28.632 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:19:28.639 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:19:28.639 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:22:51.071 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:22:51.071 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:22:51.076 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:22:51.076 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:22:54.172 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:22:54.174 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ZJtqPAVENtTKNGKtVa59vR：索引：1:日志信息写入异常
2025-07-27 16:24:00.376 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:24:06.115 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:24:06.115 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:24:06.121 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:24:06.121 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:24:35.040 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:26:56.581 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:26:56.581 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:26:56.588 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:26:56.588 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:27:01.875 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:27:01.878 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ELoBkZJwYsTwLhWjBkdFPL：索引：1:日志信息写入异常
2025-07-27 16:27:51.203 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:27:51.209 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - iLGtQJzd6RmEiiwfKkSiWo：索引：1:日志信息写入异常
2025-07-27 16:33:02.164 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:33:12.411 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:33:12.411 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:33:12.427 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:33:12.428 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:33:12.564 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:33:12.565 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 7HKt5dCMeRrmKneg5jMGhk：索引：1:日志信息写入异常
2025-07-27 16:34:26.662 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:34:29.520 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:34:29.520 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:34:29.525 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:34:29.525 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:34:42.583 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:34:45.491 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:34:45.492 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:34:45.521 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:34:45.522 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:34:45.576 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:34:45.587 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - T93YB2JbtDvQBWataqDyTX：索引：1:日志信息写入异常
2025-07-27 16:35:02.746 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:35:02.753 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - dQdpELHAuX56kXCtvvNfxq：索引：1:日志信息写入异常
2025-07-27 16:35:38.884 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:35:41.581 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:35:41.581 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:35:41.585 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:35:41.585 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:35:41.589 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:35:41.591 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VRq5R5QybahnzksS8huZ4d：索引：1:日志信息写入异常
2025-07-27 16:36:05.034 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:36:05.039 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - CtoyVMB7eNE99rZH7kCCCK：索引：1:日志信息写入异常
2025-07-27 16:37:27.960 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:37:33.845 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:37:33.845 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:37:33.851 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:37:33.852 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:37:33.944 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:37:33.946 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - dMZgVSSM7wnYejL7cWWsPU：索引：1:日志信息写入异常
2025-07-27 16:38:21.231 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:38:24.037 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:38:24.038 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:38:24.041 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:38:24.042 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:38:49.295 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:38:52.114 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:38:52.114 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:38:52.119 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:38:52.119 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:40:04.708 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:40:07.803 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:40:07.803 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:40:07.807 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:40:07.807 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:41:04.028 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:41:04.032 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - HkBbUA2xtkuyBznuPSTUSY：索引：1:日志信息写入异常
2025-07-27 16:41:09.422 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:42:24.404 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:42:27.138 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:42:27.139 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:42:27.145 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:42:27.145 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:42:27.151 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:42:27.154 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - W7Kg8fqEWHRnXxfRHzj2Sj：索引：1:日志信息写入异常
2025-07-27 16:42:27.180 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:42:58.581 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:42:58.584 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ndKrL8V8qoykCUSuawAUws：索引：1:日志信息写入异常
2025-07-27 16:42:58.592 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResPartnerModel(res_partner)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:45:59.282 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:48:15.535 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:48:15.536 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:48:15.550 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:48:15.550 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:49:34.739 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:49:34.739 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:49:34.745 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:49:34.745 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:49:38.442 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:49:38.444 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - CFTJVRmm84J8RXYsHvG23M：索引：1:日志信息写入异常
2025-07-27 16:49:53.765 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:49:56.425 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:49:56.425 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:49:56.431 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:49:56.431 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:49:56.434 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:49:56.436 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - b4ySZS3auLS7wVeeCzNsmV：索引：1:日志信息写入异常
2025-07-27 16:49:56.473 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:51:34.350 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:51:36.974 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:51:36.974 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:51:36.981 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:51:36.981 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:51:42.965 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:51:42.967 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - KjCP7hBot4mkJ2c7ZkN7nD：索引：1:日志信息写入异常
2025-07-27 16:51:42.993 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:54:12.548 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:54:12.549 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:54:12.554 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:54:12.554 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:54:17.913 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:54:17.917 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - MQVQo9WYac8GAgJbmD7jq4：索引：1:日志信息写入异常
2025-07-27 16:54:17.949 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Error creating backref 'partner' on relationship 'ResPartnerModel.user_ids': property of that name exists on mapper 'Mapper[ResUserModel(res_user)]'
2025-07-27 16:54:43.493 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:54:49.739 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:54:49.739 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:54:49.757 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:54:49.757 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:54:54.826 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:54:54.832 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 7J8nsABDek3oe2xqVwLoPq：索引：1:日志信息写入异常
2025-07-27 16:54:54.869 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user_ids - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 16:55:24.567 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:55:28.547 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:55:28.548 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:55:28.555 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:55:28.555 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:55:39.383 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 16:55:39.390 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - LQiPVYgE7M2sYF3bnCAthb：索引：1:日志信息写入异常
2025-07-27 16:55:39.421 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Error creating backref 'partner' on relationship 'ResPartnerModel.user_ids': property of that name exists on mapper 'Mapper[ResUserModel(res_user)]'
2025-07-27 16:57:45.524 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:57:45.524 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:57:45.531 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:57:45.531 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:58:58.279 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 16:59:01.178 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 16:59:01.178 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 16:59:01.188 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 16:59:01.189 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 16:59:57.551 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:00:00.653 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:00:00.654 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:00:00.663 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:00:00.663 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:00:50.972 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:00:53.959 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:00:53.960 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:00:53.969 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:00:53.969 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:01:12.085 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:01:12.090 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - kG95wMeppUzS8t4USpbuJW：索引：1:日志信息写入异常
2025-07-27 17:01:12.131 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResPartnerModel.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:05:21.580 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:05:21.580 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:05:21.588 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:05:21.588 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:05:38.322 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:05:38.325 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - BXoJw52hbVowzPAyp3eFrH：索引：1:日志信息写入异常
2025-07-27 17:05:38.360 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,When initializing mapper Mapper[ResPartnerModel(res_partner)], expression '[user_id]' failed to locate a name ("name 'user_id' is not defined"). If this is a class name, consider adding this relationship() to the <class 'app.models.entity.system.users.ResPartnerModel'> class after both dependent classes have been defined.
2025-07-27 17:06:19.015 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:06:21.682 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:06:21.683 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:06:21.690 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:06:21.690 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:06:38.938 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:06:38.944 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - M5Rnxayk9BQqibbcxXXCp2：索引：1:日志信息写入异常
2025-07-27 17:07:50.656 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:07:53.294 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:07:53.294 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:07:53.298 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:07:53.298 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:08:05.554 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:08:05.558 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - jzoZcTLgNLDwtYwBnFPzw7：索引：1:日志信息写入异常
2025-07-27 17:08:05.599 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:09:32.561 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:09:32.564 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ZTEX5XHhH9qnWr3SkCwodN：索引：1:日志信息写入异常
2025-07-27 17:09:41.126 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:10:16.163 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:10:19.190 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:10:19.190 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:10:19.197 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:10:19.197 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:10:21.751 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:10:21.755 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ACAiCbGNhgSvHnGJP5fLZh：索引：1:日志信息写入异常
2025-07-27 17:15:10.843 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResPartnerModel(res_partner)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResPartnerModel.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:15:11.010 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:15:21.560 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:15:21.560 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:15:21.582 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:15:21.583 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:15:27.800 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:15:27.805 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - A36p54mZc3s9yurTAJboNt：索引：1:日志信息写入异常
2025-07-27 17:22:15.412 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:22:15.430 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:22:15.430 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:22:15.433 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - iDKWSTE253zCXUo6emcvTa：索引：1:日志信息写入异常
2025-07-27 17:22:15.436 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - jPyC5QtC4nyFCG9sL46byq：索引：1:日志信息写入异常
2025-07-27 17:22:16.428 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:22:17.093 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:22:18.318 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:22:20.954 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:22:20.955 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:22:20.959 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:22:20.959 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:22:21.568 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:22:27.599 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:22:27.599 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:22:27.605 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:22:27.605 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:22:29.701 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:22:29.703 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - QsvWE2WeFw2yXYd6WKvvAe：索引：1:日志信息写入异常
2025-07-27 17:22:34.377 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.company - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:25:57.522 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:26:00.463 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:26:00.463 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:26:00.468 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:26:00.469 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:27:40.377 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:27:43.495 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:27:43.496 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:27:43.505 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:27:43.505 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:28:20.065 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:28:20.070 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - fJBpbsFtweitodWLPG4tMg：索引：1:日志信息写入异常
2025-07-27 17:28:25.395 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 17:29:27.502 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:29:30.154 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:29:30.155 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:29:30.160 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:29:30.160 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:29:46.460 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:29:46.468 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - XfSZQYRRnaQJjxHdMkYCp3：索引：1:日志信息写入异常
2025-07-27 17:29:48.588 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.roles - there are multiple foreign key paths linking the tables via secondary table 'res_user_roles'.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference from the secondary table to each of the parent and child tables.
2025-07-27 17:33:54.747 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:33:57.545 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:33:57.546 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:33:57.552 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:33:57.553 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:34:11.437 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:34:11.443 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VsKAASfqNTgcAv86TvWtJb：索引：1:日志信息写入异常
2025-07-27 17:34:13.677 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,When initializing mapper Mapper[ResRoleModel(res_role)], expression 'ResDeptModel' failed to locate a name ('ResDeptModel'). If this is a class name, consider adding this relationship() to the <class 'app.models.entity.system.users.ResRoleModel'> class after both dependent classes have been defined.
2025-07-27 17:35:55.560 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:35:58.213 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:35:58.214 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:35:58.218 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:35:58.218 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:36:12.931 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:36:12.935 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VWyxwvRuKwEYG25yNBCyS6：索引：1:日志信息写入异常
2025-07-27 17:36:15.312 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,When initializing mapper Mapper[ResRoleModel(res_role)], expression 'ResDeptModel' failed to locate a name ('ResDeptModel'). If this is a class name, consider adding this relationship() to the <class 'app.models.entity.system.users.ResRoleModel'> class after both dependent classes have been defined.
2025-07-27 17:38:03.612 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:38:06.471 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:38:06.471 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:38:06.475 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:38:06.476 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:38:17.144 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:38:17.148 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - m7HoHNdMVKcS28bzSUAwvc：索引：1:日志信息写入异常
2025-07-27 17:38:21.379 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,relationship 'users' expects a class or a mapper argument (received: <class 'sqlalchemy.sql.schema.Table'>)
2025-07-27 17:38:41.950 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:38:41.952 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 3E4ameKHcyLVXVPSTgBnZN：索引：1:日志信息写入异常
2025-07-27 17:40:52.431 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResPositionModel(res_position)]'. Original exception was: relationship 'users' expects a class or a mapper argument (received: <class 'sqlalchemy.sql.schema.Table'>)
2025-07-27 17:40:52.588 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:40:55.249 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:40:55.249 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:40:55.261 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:40:55.261 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:41:01.661 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:41:01.664 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - Bd4ZVtupXbMNbkMZ8qkEoj：索引：1:日志信息写入异常
2025-07-27 17:45:16.716 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:45:16.716 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:45:16.725 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:45:16.725 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:45:24.713 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:45:24.719 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - A7GW4BHurvFZuJAUgMRrK8：索引：1:日志信息写入异常
2025-07-27 17:46:50.372 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:46:50.372 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:46:50.377 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:46:50.377 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:47:02.714 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:47:02.720 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 3bvtb5mXJ6oGEsCeVYWEfo：索引：1:日志信息写入异常
2025-07-27 17:48:01.146 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:48:01.146 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:48:01.162 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:48:01.162 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:48:01.346 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:48:01.372 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - Mp7x2SzAuSwmMBWxwXtkMw：索引：1:日志信息写入异常
2025-07-27 17:49:28.340 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:49:30.985 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:49:30.985 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:49:30.989 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:49:30.990 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:49:35.237 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 17:49:40.883 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 17:49:40.883 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 17:49:40.888 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 17:49:40.888 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 17:49:41.124 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 17:49:41.126 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - dxwRVuap3xNU3X4fSydLur：索引：1:日志信息写入异常
2025-07-27 18:10:26.681 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:10:26.682 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:10:26.695 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:10:26.696 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:10:38.506 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:10:38.510 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 6w42KxqqoxNjLxVoP4yZDX：索引：1:日志信息写入异常
2025-07-27 18:13:40.466 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:13:40.467 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:13:40.482 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:13:40.483 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:14:08.868 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:14:08.868 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:14:08.872 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:14:08.872 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:14:09.613 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:14:09.617 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - nENw9TpDrkdJ6sY8DDkYsT：索引：1:日志信息写入异常
2025-07-27 18:14:57.020 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:14:57.025 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - MjbjM8JKNtFXv9vFQ3L9AV：索引：1:日志信息写入异常
2025-07-27 18:16:28.016 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 18:16:34.592 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:16:34.592 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:16:34.621 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:16:34.624 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:16:34.639 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:16:34.644 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - 7QrFgSMYjq7d9thEKxJA2E：索引：1:日志信息写入异常
2025-07-27 18:17:01.211 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:17:01.216 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - GQtGz3APkFkFBVctkd6rVa：索引：1:日志信息写入异常
2025-07-27 18:17:53.891 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:17:53.892 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:17:53.899 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:17:53.900 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:18:01.151 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:18:01.157 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - KTnnh9Jux3FWyWuvKcwUC5：索引：1:日志信息写入异常
2025-07-27 18:18:57.482 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 18:19:04.002 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:19:04.002 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:19:04.034 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:19:04.034 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:19:04.046 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:19:04.050 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - QiKrthphDDG2vMj9Eypfsq：索引：1:日志信息写入异常
2025-07-27 18:21:19.385 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 18:21:22.224 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:21:22.224 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:21:22.232 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:21:22.233 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:23:55.843 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 18:23:58.508 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:23:58.509 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:23:58.512 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:23:58.513 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:23:58.517 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:23:58.519 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - K7mZ6msPAsdpXFcQDpgiEo：索引：1:日志信息写入异常
2025-07-27 18:24:33.803 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 18:24:36.661 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:24:36.662 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:24:36.670 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:24:36.670 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:24:36.679 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:24:36.682 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - S8cBmxHcEEijZKcVtuu42y：索引：1:日志信息写入异常
2025-07-27 18:25:17.424 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 18:25:21.288 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:25:21.288 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:25:21.295 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:25:21.295 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:25:23.430 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:25:23.433 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - mmc5vQVKeAedTc9P3Qkg8z：索引：1:日志信息写入异常
2025-07-27 18:25:49.960 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 18:25:53.224 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:25:53.224 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:25:53.231 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:25:53.232 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:25:53.235 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:25:53.237 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - k4QNRQU7u2DeXMYmCb8p9u：索引：1:日志信息写入异常
2025-07-27 18:26:50.003 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 18:26:53.381 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:26:53.382 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:26:53.386 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:26:53.387 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:26:53.391 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:26:53.393 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - MzZGpway2MZY6KXzJmrTCx：索引：1:日志信息写入异常
2025-07-27 18:27:23.818 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '57', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:27:23.821 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - JrJ6ZRazBkjKVF4DdQuHLS：索引：1:日志信息写入异常
2025-07-27 18:28:47.505 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/refresh/token
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '216', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:28:47.508 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "c7gAQ8XHBhgc4bybEfiKQV", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/refresh/token", "method": "POST", "ip": "127.0.0.1", "params": {"body": {"refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOnRydWUsImV4cCI6MTc1NDIxNjg0MywiaWF0IjoxNzUzNjEyMDQzLCJuYW1lIjoiYWRtaW4ifQ.erOloOrKamiKyZoFViDpS-B3nFDIaYP1imMJ_3BHrdA"}}, "ts": "2025-07-27 18:28:47"}}
2025-07-27 18:29:30.956 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:29:30.961 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - ZnFtJLgatvQBDNRZy55fNJ：索引：1:日志信息写入异常
2025-07-27 18:29:39.557 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer undefined', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:29:39.561 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6JyThY5F49CagtNRkSCey9", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 18:29:39"}}
2025-07-27 18:31:21.165 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:31:21.165 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:31:21.172 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:31:21.172 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:31:25.550 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:31:25.556 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "NJ6rVbjKLEFG99EL3EyNdT", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 18:31:25"}}
2025-07-27 18:31:25.909 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:31:25.910 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "4GFxNyHVJ2vUCUsVJMSePx", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 18:31:25"}}
2025-07-27 18:31:38.845 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:31:38.847 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - bFzDyFGTJvea6SbcvB3wpz：索引：1:日志信息写入异常
2025-07-27 18:31:45.385 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer undefined', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:31:45.387 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "4LRj6s98jLMLrenHD33fCh", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 18:31:45"}}
2025-07-27 18:32:16.779 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:32:16.782 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - M8PyzAomafpJNrLSJyZMLP：索引：1:日志信息写入异常
2025-07-27 18:32:23.441 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer undefined', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:32:23.442 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "XsShz5ACkNJq4TzZ5ioqsZ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 18:32:23"}}
2025-07-27 18:33:34.197 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:33:34.197 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:33:34.203 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:33:34.203 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 18:33:40.522 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:33:40.526 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "baEr9hZXWSBAqN4hephduv", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 18:33:40"}}
2025-07-27 18:33:40.798 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:33:40.801 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "Sh3ev8on7EJpfTk8VuFSb5", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 18:33:40"}}
2025-07-27 18:33:48.957 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:33:48.959 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VDdDW8SxrQLUySYyMyfKBs：索引：1:日志信息写入异常
2025-07-27 18:33:59.496 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM2OTg4MjksImlhdCI6MTc1MzYxMjQyOSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjMyNzc1MDI0LTVjNjItNDZlMi1iYzEzLTNmMjc3MWUxMDJkOCJ9.4UpmOgOotM6SxqLxbKm7Iszw2JO7PZd5FsM5Qx139OI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 18:33:59.497 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "56vevBmEZtQucNbxaA4RwZ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 18:33:59"}}
2025-07-27 18:35:16.972 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 18:35:16.972 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 18:35:16.979 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 18:35:16.979 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 19:57:38.683 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 20:18:25.701 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 20:18:25.701 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 20:18:25.722 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 20:18:25.722 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 20:19:42.489 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 20:19:46.246 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 20:19:46.246 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 20:19:46.257 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 20:19:46.257 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 20:21:26.712 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 20:21:29.772 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 20:21:29.772 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 20:21:29.776 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 20:21:29.776 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 20:26:39.616 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 20:26:42.854 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 20:26:42.855 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 20:26:42.864 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 20:26:42.864 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 20:29:34.562 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 20:29:37.510 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 20:29:37.511 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 20:29:37.513 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 20:29:37.513 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 20:50:07.449 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 20:50:07.450 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 20:50:07.472 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 20:50:07.473 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 20:52:36.650 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:25:18.070 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:25:18.071 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:25:18.093 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:25:18.094 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:26:49.475 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:26:53.902 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:26:53.904 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:26:53.930 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:26:53.930 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:28:05.090 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:33:46.034 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:33:46.034 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:33:46.062 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:33:46.062 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:35:10.105 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:35:12.867 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:35:12.867 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:35:12.872 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:35:12.873 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:41:41.120 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:41:44.473 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:41:44.474 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:41:44.484 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:41:44.484 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:42:11.017 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:42:15.043 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:42:15.044 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:42:15.054 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:42:15.055 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:42:38.950 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:42:41.798 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:42:41.799 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:42:41.805 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:42:41.805 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:48:21.064 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:48:25.044 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:48:25.045 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:48:25.057 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:48:25.057 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:49:00.764 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:49:00.765 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:49:00.777 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:49:00.777 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 21:58:24.607 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 21:58:29.993 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 21:58:29.994 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 21:58:30.030 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 21:58:30.030 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:07:17.113 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 22:07:21.297 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:07:21.297 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:07:21.315 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:07:21.316 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:10:39.320 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:10:39.321 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:10:39.328 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:10:39.328 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:25:16.367 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 22:25:21.136 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:25:21.137 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:25:21.166 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:25:21.166 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:36:17.026 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 22:36:20.088 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:36:20.088 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:36:20.100 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:36:20.101 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:36:28.995 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 22:36:29.000 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "J96xzKD2rKwEHJN3E6Ye5N", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 22:36:28"}}
2025-07-27 22:36:29.622 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 22:36:29.625 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6rPLK2fHWwthtaeipZXvXk", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 22:36:29"}}
2025-07-27 22:36:40.349 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 22:36:40.351 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - MTCU7fS62HGYck8q5xMGiF：索引：1:日志信息写入异常
2025-07-27 22:36:48.896 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MTM0MDAsImlhdCI6MTc1MzYyNzAwMCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmOGFhZTJmLTdiZmEtNGZkMi1hZmI3LTU0NjZmOWU1NWI2ZiJ9.wpmV8ArYU3rmvgPnnDsEC5Dp4j4Q7a_n-QaCWPpyA5M', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 22:36:48.898 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "BDXY8PfiiZQb7AFJQ67jo2", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 22:36:48"}}
2025-07-27 22:39:58.496 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:39:58.496 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:39:58.505 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:39:58.505 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:40:15.156 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MTM0MDAsImlhdCI6MTc1MzYyNzAwMCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmOGFhZTJmLTdiZmEtNGZkMi1hZmI3LTU0NjZmOWU1NWI2ZiJ9.wpmV8ArYU3rmvgPnnDsEC5Dp4j4Q7a_n-QaCWPpyA5M', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 22:40:15.162 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "7ThcCGKvJioypHkkZk9Vka", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 22:40:15"}}
2025-07-27 22:40:15.181 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/user/info/current,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 22:43:44.160 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 22:43:46.942 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:43:46.942 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:43:46.948 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:43:46.948 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:44:51.872 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 22:47:15.061 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:47:15.061 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:47:15.069 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:47:15.069 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:47:20.773 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MTM0MDAsImlhdCI6MTc1MzYyNzAwMCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmOGFhZTJmLTdiZmEtNGZkMi1hZmI3LTU0NjZmOWU1NWI2ZiJ9.wpmV8ArYU3rmvgPnnDsEC5Dp4j4Q7a_n-QaCWPpyA5M', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 22:47:20.777 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "X4f6Rusfnp6zKE6vmVofEY", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 22:47:20"}}
2025-07-27 22:47:20.805 | thread_id:8409849600 | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/user/info/current,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-27 22:52:44.457 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 22:52:47.291 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:52:47.292 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:52:47.301 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:52:47.301 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:53:54.531 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 22:53:57.289 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:53:57.289 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:53:57.293 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:53:57.293 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:55:06.480 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:55:06.481 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:55:06.486 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:55:06.486 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:56:21.766 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 22:56:25.601 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 22:56:25.602 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 22:56:25.617 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 22:56:25.617 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 22:57:52.391 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MTM0MDAsImlhdCI6MTc1MzYyNzAwMCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmOGFhZTJmLTdiZmEtNGZkMi1hZmI3LTU0NjZmOWU1NWI2ZiJ9.wpmV8ArYU3rmvgPnnDsEC5Dp4j4Q7a_n-QaCWPpyA5M', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.1583046421.1749780438; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:127.0.0.1
2025-07-27 22:57:52.404 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "mS9kmAZFAaBNwH5u9PWeJ8", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "127.0.0.1", "params": {}, "ts": "2025-07-27 22:57:52"}}
2025-07-27 23:00:28.754 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:00:31.553 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:00:31.553 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:00:31.566 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:00:31.566 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:01:04.758 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:01:04.759 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:01:04.762 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:01:04.762 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:04:25.661 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:04:30.214 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:04:30.215 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:04:30.226 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:04:30.227 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:05:07.878 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:08:17.719 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:08:17.720 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:08:17.734 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:08:17.734 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:10:12.904 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:10:15.694 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:10:15.694 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:10:15.705 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:10:15.705 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:11:52.816 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:11:55.664 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:11:55.665 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:11:55.671 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:11:55.672 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:14:40.266 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:14:43.248 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:14:43.249 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:14:43.258 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:14:43.259 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:15:09.577 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:15:12.521 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:15:12.522 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:15:12.528 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:15:12.528 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:18:05.613 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:18:05.614 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:18:05.623 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:18:05.623 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:22:59.602 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:23:03.788 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:23:03.789 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:23:03.806 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:23:03.806 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:23:48.545 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:23:51.361 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:23:51.362 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:23:51.369 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:23:51.369 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:25:23.112 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:25:27.046 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:25:27.046 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:25:27.053 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:25:27.053 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:25:36.050 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:25:39.738 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:25:39.739 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:25:39.744 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:25:39.744 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:30:10.990 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:32:38.356 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:32:38.356 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:32:38.364 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:32:38.365 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:33:53.501 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:33:56.610 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:33:56.611 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:33:56.618 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:33:56.619 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:34:54.354 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:34:57.100 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:34:57.101 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:34:57.107 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:34:57.107 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:38:33.602 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:39:54.689 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:39:54.690 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:39:54.705 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:39:54.705 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:42:37.175 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:42:40.252 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:42:40.252 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:42:40.257 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:42:40.257 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:43:16.413 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:43:19.892 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:43:19.893 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:43:19.898 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:43:19.898 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:47:13.474 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:47:16.293 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:47:16.293 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:47:16.300 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:47:16.300 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:56:12.117 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:56:14.883 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:56:14.883 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:56:14.895 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:56:14.895 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:56:58.043 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:57:00.946 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:57:00.946 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:57:00.950 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:57:00.950 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-27 23:58:33.047 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-27 23:58:36.849 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-27 23:58:36.850 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-27 23:58:36.858 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-27 23:58:36.858 | thread_id:8409849600 | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
