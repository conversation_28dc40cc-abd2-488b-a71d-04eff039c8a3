2025-07-28 00:06:15.052 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:06:15.053 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:06:15.063 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:06:15.063 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:10:36.864 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:10:39.698 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:10:39.699 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:10:39.706 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:10:39.706 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:11:25.915 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:11:25.918 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "ZM7Dqt6yZ2aKeDJEFPgo27", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 00:11:25"}}
2025-07-28 00:11:26.713 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:11:26.716 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "eLxJwWSQFW3LbeAvVe8s37", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 00:11:26"}}
2025-07-28 00:11:33.802 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:11:33.806 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - XhawSRdwU6FzAvTfMug7R7：索引：1:日志信息写入异常
2025-07-28 00:11:33.827 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:11:38.649 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer undefined', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:11:38.652 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "fFGTPFMRFfzWQbZxRytUtd", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 00:11:38"}}
2025-07-28 00:11:38.659 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:61 - 请求地址：http://localhost:9099/v1/system/user/info/current,token无效,None
2025-07-28 00:11:43.030 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer undefined', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:11:43.031 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "H2KCw3YW5gaefuYfRGqgGB", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 00:11:43"}}
2025-07-28 00:11:43.031 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:token_auth_exception_handler:61 - 请求地址：http://localhost:9099/v1/system/user/info/current,token无效,None
2025-07-28 00:12:17.014 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:12:17.017 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "emBkDmkw3DKLLMNegBdQhK", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 00:12:17"}}
2025-07-28 00:12:17.109 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:12:17.117 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6SxUsAZAALX7YvNUvo5TVy", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 00:12:17"}}
2025-07-28 00:12:26.095 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:12:26.097 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - VVVohMNAfj6hMPTbU6JSrq：索引：1:日志信息写入异常
2025-07-28 00:12:26.103 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:13:18.700 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:13:18.703 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - QeYfzSQhPdY9vJdnBP2gX4：索引：1:日志信息写入异常
2025-07-28 00:13:26.423 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:14:13.509 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:17:10.821 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - CLBsoYV57yw7wwGCFwzwGW：索引：1:日志信息写入异常
2025-07-28 00:17:11.922 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResUserModel(res_user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:17:25.121 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:17:31.861 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:17:31.861 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:17:31.868 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:17:31.868 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:17:31.882 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:17:36.875 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - CAkfqZffuSFpq5znRfg7oH：索引：1:日志信息写入异常
2025-07-28 00:17:43.866 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:21:20.503 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:21:20.504 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:21:20.513 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:21:20.513 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:21:23.483 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:21:23.485 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - M2ecnwuwJWkoDS7gsYmwbR：索引：1:日志信息写入异常
2025-07-28 00:21:28.933 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:22:13.619 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:22:13.620 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:22:13.633 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:22:13.633 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:22:20.831 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:24:27.752 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:24:27.752 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:24:27.756 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:24:27.756 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:24:28.928 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:24:32.276 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "UUK6qWY8iqNXCNvZ3YTsXk", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"grant_type": "password", "username": "admin", "password": "admin"}}, "ts": "2025-07-28 00:24:28"}}
2025-07-28 00:24:32.298 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:28:24.135 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:28:27.529 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:28:27.529 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:28:27.539 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:28:27.539 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:29:34.727 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:29:37.697 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:29:37.697 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:29:37.704 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:29:37.704 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:30:04.430 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:30:04.430 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:30:04.433 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:30:04.433 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:31:13.371 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:31:13.377 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "enMVKffPvBDBRPXDPUcx8Y", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"grant_type": "password", "username": "admin", "password": "admin"}}, "ts": "2025-07-28 00:31:13"}}
2025-07-28 00:31:13.412 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.company and back-reference ResCompanyModel.users are both of the same direction <RelationshipDirection.ONETOMANY: 1>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:34:25.758 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:34:28.625 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:34:28.625 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:34:28.630 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:34:28.630 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:34:39.889 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:34:39.893 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "XmzX9GWD7A7waWnH3LuJAu", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"grant_type": "password", "username": "admin", "password": "admin"}}, "ts": "2025-07-28 00:34:39"}}
2025-07-28 00:34:39.926 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.company and back-reference ResCompanyModel.users are both of the same direction <RelationshipDirection.ONETOMANY: 1>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:36:04.525 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:36:07.259 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:36:07.260 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:36:07.262 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:36:07.262 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:36:22.092 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:36:22.095 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "hrX4BikARZ6sUmy9qVLCAr", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"grant_type": "password", "username": "admin", "password": "admin"}}, "ts": "2025-07-28 00:36:22"}}
2025-07-28 00:36:22.133 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,For many-to-one relationship ResDepartmentsModel.parent, delete-orphan cascade is normally configured only on the "one" side of a one-to-many relationship, and not on the "many" side of a many-to-one or many-to-many relationship.  To force this relationship to allow a particular "ResDepartmentsModel" object to be referenced by only a single "ResDepartmentsModel" object at a time via the ResDepartmentsModel.parent relationship, which would allow delete-orphan cascade to take place in this direction, set the single_parent=True flag. (Background on this error at: https://sqlalche.me/e/20/bbf0)
2025-07-28 00:36:41.174 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:36:41.183 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "DbdzYi7bdAuQZEcdzj5NQN", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:36:41"}}
2025-07-28 00:36:41.193 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[ResDepartmentsModel(res_department)]'. Original exception was: For many-to-one relationship ResDepartmentsModel.parent, delete-orphan cascade is normally configured only on the "one" side of a one-to-many relationship, and not on the "many" side of a many-to-one or many-to-many relationship.  To force this relationship to allow a particular "ResDepartmentsModel" object to be referenced by only a single "ResDepartmentsModel" object at a time via the ResDepartmentsModel.parent relationship, which would allow delete-orphan cascade to take place in this direction, set the single_parent=True flag. (Background on this error at: https://sqlalche.me/e/20/bbf0)
2025-07-28 00:39:22.637 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:39:25.774 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:39:25.775 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:39:25.778 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:39:25.778 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:39:44.214 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:39:44.217 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "mCViVxjbEx776rZpPq9Jr6", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:39:44"}}
2025-07-28 00:39:44.268 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/auth/login,When initializing mapper Mapper[ResPositionModel(res_position)], expression 'res_employee_positions_rel' failed to locate a name ("name 'res_employee_positions_rel' is not defined"). If this is a class name, consider adding this relationship() to the <class 'app.models.entity.system.system.ResPositionModel'> class after both dependent classes have been defined.
2025-07-28 00:41:42.398 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:41:45.219 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:41:45.220 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:41:45.224 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:41:45.224 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:42:00.032 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:42:00.037 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "8Hh7QZsJK6BqyVMibggKGW", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:42:00"}}
2025-07-28 00:42:00.099 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:44:46.337 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:44:49.140 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:44:49.141 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:44:49.151 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:44:49.151 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:46:54.439 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:46:54.440 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:46:54.446 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:46:54.446 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:47:03.712 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:47:03.718 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "PSGCo5A5LzAjLX9MHWRGEn", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:47:03"}}
2025-07-28 00:47:03.737 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:47:42.938 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:47:42.938 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:47:42.942 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:47:42.942 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:47:50.654 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:47:50.663 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "cj2Fc8PWRTvGKvxEyMsh6o", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:47:50"}}
2025-07-28 00:47:50.724 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:48:01.803 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:48:04.584 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:48:04.585 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:48:04.589 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:48:04.589 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:49:46.880 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:49:49.774 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:49:49.775 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:49:49.796 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:49:49.796 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:49:56.165 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:49:56.179 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "hBtc4Tz3xyCbr6DyUP8VkB", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:49:56"}}
2025-07-28 00:49:56.263 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:50:24.386 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:50:27.168 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:50:27.169 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:50:27.173 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:50:27.173 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:50:37.605 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:50:37.608 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "SkJnGJD3oJJqr5mzVbot4s", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:50:37"}}
2025-07-28 00:50:37.670 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:51:08.956 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:51:11.686 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:51:11.687 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:51:11.692 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:51:11.692 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:51:17.521 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:51:17.524 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "3LdgaxEtHG6NjNLyJ67ZiJ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:51:17"}}
2025-07-28 00:51:17.592 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,ResUserModel.employee and back-reference ResEmployeeModel.user are both of the same direction <RelationshipDirection.MANYTOONE: 2>.  Did you mean to set remote_side on the many-to-one side ?
2025-07-28 00:53:34.966 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:53:34.966 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:53:34.969 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:53:34.969 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:53:41.406 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:53:41.410 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "gXU2C7bx9jEFCAgvYf94DJ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:53:41"}}
2025-07-28 00:53:41.473 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResEmployeeModel.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 00:55:52.023 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:55:55.081 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:55:55.081 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:55:55.093 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:55:55.093 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:57:31.237 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 00:57:38.773 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 00:57:38.773 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 00:57:38.780 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 00:57:38.781 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 00:57:45.687 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 00:57:45.690 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "3NtguBNkVxwnAu3gsGQ2mZ", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 00:57:45"}}
2025-07-28 00:57:45.742 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResEmployeeModel.user - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 01:00:58.612 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 01:01:02.201 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 01:01:02.201 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 01:01:02.222 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 01:01:02.222 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 01:01:09.617 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 01:01:09.622 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "JWNLvdJ2XqgwwcJFaauQ2G", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 01:01:09"}}
2025-07-28 01:56:28.081 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 01:56:28.081 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 01:56:28.104 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 01:56:28.104 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 01:56:28.846 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 01:56:28.850 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "dR5vk4dforK8ogNmGuzFbh", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 01:56:28"}}
2025-07-28 01:56:28.865 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_exception_handler:132 - 请求地址：http://localhost:9099/v1/system/auth/login,Could not determine join condition between parent/child tables on relationship ResUserModel.employee - there are multiple foreign key paths linking the tables.  Specify the 'foreign_keys' argument, providing a list of those columns which should be counted as containing a foreign key reference to the parent table.
2025-07-28 01:57:05.973 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 01:57:05.973 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 01:57:05.979 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 01:57:05.979 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 01:57:10.283 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '51', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/x-www-form-urlencoded', 'sec-ch-ua-mobile': '?0', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 01:57:10.343 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "BBqGS6z3MAsY4FUbpPAnq6", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"username": "admin", "password": "admin", "captcha_key": "", "captcha": ""}}, "ts": "2025-07-28 01:57:10"}}
2025-07-28 01:57:24.894 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 01:57:24.907 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "47puxt5QgJfs5TK3XzEL8x", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"grant_type": "password", "username": "admin", "password": "admin"}}, "ts": "2025-07-28 01:57:24"}}
2025-07-28 01:57:39.402 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MjU0NDQsImlhdCI6MTc1MzYzOTA0NCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6ImNlMWI3NDQ4LTY0OGQtNDBjZS1iYmViLWFlNjRmMzBkMjJjNSJ9.2V-czZmnZ-6dSCG1rgANmtwJ4E1upAby-EKSei484T0', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 01:57:39.406 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "X6F6oua7wBuEYpbfqTnSVp", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 01:57:39"}}
2025-07-28 01:58:16.418 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MjU0NDQsImlhdCI6MTc1MzYzOTA0NCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6ImNlMWI3NDQ4LTY0OGQtNDBjZS1iYmViLWFlNjRmMzBkMjJjNSJ9.2V-czZmnZ-6dSCG1rgANmtwJ4E1upAby-EKSei484T0', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 01:58:16.422 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "93nsGpcSjZJLnc644wEZ5g", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 01:58:16"}}
2025-07-28 02:02:06.368 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 02:02:15.101 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 02:02:15.102 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 02:02:15.135 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 02:02:15.135 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 02:02:15.150 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MjU0NDQsImlhdCI6MTc1MzYzOTA0NCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6ImNlMWI3NDQ4LTY0OGQtNDBjZS1iYmViLWFlNjRmMzBkMjJjNSJ9.2V-czZmnZ-6dSCG1rgANmtwJ4E1upAby-EKSei484T0', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 02:02:15.155 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "i4ao7bvWkV8PNsAbsighnG", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 02:02:15"}}
2025-07-28 02:02:22.997 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 02:02:31.426 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 02:02:31.426 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 02:02:31.446 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 02:02:31.446 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 02:02:31.452 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MjU0NDQsImlhdCI6MTc1MzYzOTA0NCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6ImNlMWI3NDQ4LTY0OGQtNDBjZS1iYmViLWFlNjRmMzBkMjJjNSJ9.2V-czZmnZ-6dSCG1rgANmtwJ4E1upAby-EKSei484T0', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 02:02:31.455 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "G97NFeSTjPsN2M6bLCv8xp", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 02:02:31"}}
2025-07-28 02:02:32.039 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/user/info/current,greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-28 02:05:59.408 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 02:05:59.408 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 02:05:59.423 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 02:05:59.424 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 02:06:03.348 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MjU0NDQsImlhdCI6MTc1MzYzOTA0NCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6ImNlMWI3NDQ4LTY0OGQtNDBjZS1iYmViLWFlNjRmMzBkMjJjNSJ9.2V-czZmnZ-6dSCG1rgANmtwJ4E1upAby-EKSei484T0', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 02:06:03.355 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "ABcABLaJS6DjVdpAuUxpEn", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 02:06:03"}}
2025-07-28 02:06:03.782 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:sqlalchemy_invalid_request_rrror_handler:140 - 请求地址：http://localhost:9099/v1/system/user/info/current,greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-28 02:06:14.972 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 02:06:22.775 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 02:06:22.775 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 02:06:22.781 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 02:06:22.781 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 02:06:22.787 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MjU0NDQsImlhdCI6MTc1MzYzOTA0NCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6ImNlMWI3NDQ4LTY0OGQtNDBjZS1iYmViLWFlNjRmMzBkMjJjNSJ9.2V-czZmnZ-6dSCG1rgANmtwJ4E1upAby-EKSei484T0', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 02:06:22.790 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "54NUWfZ5cBNy6ZN74HgHYi", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 02:06:22"}}
2025-07-28 02:09:23.746 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 02:09:23.747 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 02:09:23.757 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 02:09:23.757 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 02:09:24.764 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3MjU0NDQsImlhdCI6MTc1MzYzOTA0NCwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6ImNlMWI3NDQ4LTY0OGQtNDBjZS1iYmViLWFlNjRmMzBkMjJjNSJ9.2V-czZmnZ-6dSCG1rgANmtwJ4E1upAby-EKSei484T0', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 02:09:24.767 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "WWzwMEzP6sEUsBjzakAQqo", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 02:09:24"}}
2025-07-28 10:08:58.783 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 10:08:58.784 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 10:08:58.806 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 10:08:58.806 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 10:08:59.104 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/docs
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'cache-control': 'max-age=0', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 10:08:59.105 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "X26DyWspUZsdg5UrL94QcL", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/docs", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 10:08:59"}}
2025-07-28 10:08:59.793 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/openapi.json
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json,*/*', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 10:08:59.795 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "5egB3X5tLESEzusz3NHSSt", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/openapi.json", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 10:08:59"}}
2025-07-28 10:09:08.754 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:POST url:http://localhost:9099/v1/system/auth/login
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'content-length': '49', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Basic Og==', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'x-requested-with': 'XMLHttpRequest', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/x-www-form-urlencoded', 'origin': 'http://localhost:9099', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 10:09:08.757 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "EpuWUmYYsGUszQ6voiogUp", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/auth/login", "method": "POST", "ip": "12*******", "params": {"from": {"grant_type": "password", "username": "admin", "password": "admin"}}, "ts": "2025-07-28 10:09:08"}}
2025-07-28 10:09:16.308 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 10:09:16.311 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "6WaZRgPdGq2tqjcav959Mt", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 10:09:16"}}
2025-07-28 10:09:57.452 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:94 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 10:11:08.648 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 10:11:15.380 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 10:11:15.380 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 10:11:15.396 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 10:11:15.397 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 10:12:30.177 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 10:12:33.154 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 10:12:33.154 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 10:12:33.158 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 10:12:33.158 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 10:12:33.162 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 10:12:33.164 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "LYJgiyFBMxozk6c3k9Jetg", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 10:12:33"}}
2025-07-28 10:13:30.079 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 10:13:32.507 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 10:13:35.477 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 10:13:35.478 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 10:13:35.484 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 10:13:35.484 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 10:13:37.590 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 10:13:37.592 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "cnnoAQ8Bjoe748YtSrY2pt", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 10:13:37"}}
2025-07-28 10:14:02.000 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 10:14:51.525 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 10:14:51.532 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "hFMR6V2KtrSbkLho2S4HVs", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 10:14:51"}}
2025-07-28 10:34:56.017 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 11:35:55.055 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 11:35:58.853 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 11:35:58.854 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 11:35:58.879 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 11:35:58.880 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 11:36:50.287 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 11:36:50.290 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "LNiGY7HTRktWCGGatoxkKp", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 11:36:50"}}
2025-07-28 11:37:08.057 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 11:40:03.591 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 11:40:06.491 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 11:40:06.491 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 11:40:06.500 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 11:40:06.500 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 11:40:10.195 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 11:40:10.199 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "Xn4LFJc7JCWgrPKv8Db4Gx", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 11:40:10"}}
2025-07-28 11:40:24.508 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 11:42:15.666 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 11:42:18.797 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 11:42:18.798 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 11:42:18.808 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 11:42:18.808 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 11:48:20.530 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/static
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 11:48:20.538 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "d5t2CVRmCfSGjnQgSr3A4z", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/static", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 11:48:20"}}
2025-07-28 11:48:20.541 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/static/
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 11:48:20.542 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "HdWfko3VXCoU6eG8V9ipHH", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/static/", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 11:48:20"}}
2025-07-28 11:48:58.893 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/static/avatar/face.png
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-ch-ua-platform': '"macOS"', 'upgrade-insecure-requests': '1', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'navigate', 'sec-fetch-user': '?1', 'sec-fetch-dest': 'document', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 11:48:58.902 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "bvPqUBQ6bNfXsk3UUSbMHw", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/static/avatar/face.png", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 11:48:58"}}
2025-07-28 11:49:18.539 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 11:49:23.156 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 11:49:23.156 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 11:49:23.168 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 11:49:23.168 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 11:52:17.706 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 11:52:17.724 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "XRyNq9vVHAS5i45itMHgtL", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 11:52:17"}}
2025-07-28 11:52:20.852 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:pydantic_validation_exception_handler:95 - 请求地址：http://localhost:9099/v1/system/user/info/current,()
2025-07-28 11:57:22.779 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 11:57:25.959 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 11:57:25.959 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 11:57:25.982 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 11:57:25.982 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:01:37.202 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 12:01:43.114 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 12:01:43.115 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 12:01:43.132 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 12:01:43.132 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:03:05.249 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 12:03:11.150 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 12:03:11.150 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 12:03:11.191 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 12:03:11.191 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:03:11.199 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:03:11.204 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "N3NbFUKE43puoGCoC6i2vz", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:03:11"}}
2025-07-28 12:04:07.757 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:04:07.768 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "AGLTYfXS2xr5vguGYUhqsY", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:04:07"}}
2025-07-28 12:07:08.278 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 12:13:09.727 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 12:13:09.728 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 12:13:09.780 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 12:13:09.780 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:24:42.312 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 12:24:42.313 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 12:24:42.335 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 12:24:42.335 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:43:41.068 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 12:43:45.816 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 12:43:45.817 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 12:43:45.839 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 12:43:45.839 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:43:45.845 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:43:45.846 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "fdb8hPmHHGxdkqWL5MrKn8", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:43:45"}}
2025-07-28 12:45:22.565 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:45:22.578 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "V4xHjc5fqn7FrSjmXq2JnN", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:45:22"}}
2025-07-28 12:47:44.256 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 12:47:48.604 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 12:47:48.606 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 12:47:48.615 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 12:47:48.615 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:47:48.626 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:47:48.631 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "N6aK9xCFwxESofcuR3Gcwv", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:47:48"}}
2025-07-28 12:48:44.420 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:48:44.430 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "7Cz4zvwA7bWqBo5U7dUvXD", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:48:44"}}
2025-07-28 12:48:46.395 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:48:46.400 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "iPwNbMMHP6HvfWcTD3kBL9", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:48:46"}}
2025-07-28 12:49:16.787 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 12:49:16.787 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 12:49:16.792 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 12:49:16.792 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:49:19.440 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:49:19.444 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "a9waKfdnrKkRePZcxhzhH8", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:49:19"}}
2025-07-28 12:49:49.247 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:49:49.251 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "23LQczEhTrXvy92WMNL83M", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:49:49"}}
2025-07-28 12:59:34.376 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 12:59:34.376 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 12:59:34.392 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 12:59:34.392 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 12:59:38.142 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 12:59:38.150 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "T9JEHdFPiDDzjqKFKdrQF4", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 12:59:38"}}
2025-07-28 13:00:52.705 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:00:56.190 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 13:00:56.190 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:00:56.206 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:00:56.206 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 13:05:05.626 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:05:09.827 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 13:05:09.828 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:05:09.838 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:05:09.839 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 13:05:09.843 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:116 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 13:05:09.844 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "JVE5wF29F5izD6UUpEiKFB", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:05:09"}}
2025-07-28 13:05:37.818 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:05:40.876 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 13:05:40.876 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:05:40.880 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:05:40.880 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 13:05:54.169 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:05:57.675 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 13:05:57.676 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:05:57.684 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:05:57.684 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 13:11:05.997 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:11:10.944 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 13:11:10.944 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:11:10.969 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:11:10.970 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 13:11:34.776 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 13:11:34.776 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:11:34.786 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:11:34.787 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 13:11:34.792 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:119 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 13:11:34.794 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "fWoAB3g69t4mhMMZ9bu9cz", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:11:34"}}
2025-07-28 13:12:55.262 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 13:12:55.263 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:12:55.276 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:12:55.276 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 13:12:55.320 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:logger_request:119 - 访问记录:GET url:http://localhost:9099/v1/system/user/info/current
headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
IP:12*******
2025-07-28 13:12:55.326 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:94 - {"traceid": "WbEhJPXLtYHcnVLSATzrmB", "trace_index": 1, "event_type": "request", "msg": {"useragent": null, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:12:55"}}
2025-07-28 13:26:33.087 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:25 - tdumpAdmin启动中...
2025-07-28 13:26:33.088 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:26:33.110 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:26:33.110 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:37 - tdumpAdmin启动成功...
2025-07-28 13:26:33.114 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:95 - {"traceid": "W9UiXCS7ZeZWqb7j2aA4eX", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:26:33"}}
2025-07-28 13:32:23.877 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:32:29.509 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:27 - tdumpAdmin启动中...
2025-07-28 13:32:29.510 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:32:29.530 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:32:29.530 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:39 - tdumpAdmin启动成功...
2025-07-28 13:35:56.896 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:37:32.738 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 13:37:32.739 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:37:32.758 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:37:32.758 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 13:37:35.830 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:95 - {"traceid": "5sE7Eemy9AHBMGqDfPFLPw", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:37:35"}}
2025-07-28 13:37:36.245 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - 全局异常
GETURL:http://localhost:9099/v1/system/user/info/current
Headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 151, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/exceptions/handle.py", line 28, in catch_exceptions_middleware
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 295, in __call__
    raise e
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 292, in __call__
    response = await self.app(scope, receive, send)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/api/system/user.py", line 17, in get_current_user_info
    1 / 0
    ~~^~~
ZeroDivisionError: division by zero

2025-07-28 13:38:23.061 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:38:27.500 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 13:38:27.500 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:38:27.511 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:38:27.512 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 13:38:27.518 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:95 - {"traceid": "RsoaLq4yZiUeZdcg3jFy3z", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:38:27"}}
2025-07-28 13:38:27.859 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - 全局异常
GETURL:http://localhost:9099/v1/system/user/info/current
Headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 151, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/exceptions/handle.py", line 28, in catch_exceptions_middleware
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 295, in __call__
    raise e
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 292, in __call__
    response = await self.app(scope, receive, send)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/api/system/user.py", line 17, in get_current_user_info
    1 / 0
    ~~^~~
ZeroDivisionError: division by zero

2025-07-28 13:46:23.180 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:46:26.850 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 13:46:26.851 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:46:26.884 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:46:26.884 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 13:46:26.908 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "nXnDuVUAPv58UgkCxv6xMc", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:46:26"}}
2025-07-28 13:46:27.563 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "nXnDuVUAPv58UgkCxv6xMc", "trace_index": 2, "event_type": "exception", "msg": {"error": "division by zero"}, "remarks": "请求处理异常"}
2025-07-28 13:46:27.575 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - 全局异常
GETURL:http://localhost:9099/v1/system/user/info/current
Headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 151, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/exceptions/handle.py", line 28, in catch_exceptions_middleware
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 163, in dispatch
    raise e
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/core/middlewares/logger_middleware.py", line 139, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/code/tdumpAdmin-simple-template/app/api/system/user.py", line 17, in get_current_user_info
    1 / 0
    ~~^~~
ZeroDivisionError: division by zero

2025-07-28 13:52:30.981 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:52:35.230 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 13:52:35.231 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:52:35.246 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:52:35.246 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 13:52:41.625 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "c3gWrVM4gmVkT6kZJqLwCB", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:52:41"}}
2025-07-28 13:52:41.913 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "c3gWrVM4gmVkT6kZJqLwCB", "trace_index": 2, "event_type": "exception", "msg": {"error": "division by zero"}, "remarks": "请求处理异常"}
2025-07-28 13:52:41.913 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - traceid:c3gWrVM4gmVkT6kZJqLwCB,捕获全局异常：
GETURL:http://localhost:9099/v1/system/user/info/current
Headers:Headers({'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'})
2025-07-28 13:54:42.400 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:54:48.540 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 13:54:48.542 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:54:48.565 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:54:48.565 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 13:54:56.621 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "2cH8kNiktwmnEN7AMfsGsL", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:54:48"}}
2025-07-28 13:56:06.479 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "2cH8kNiktwmnEN7AMfsGsL", "trace_index": 2, "event_type": "exception", "msg": {"error": "division by zero"}, "remarks": "请求处理异常"}
2025-07-28 13:56:06.483 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:30 - traceid:2cH8kNiktwmnEN7AMfsGsL,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
2025-07-28 13:56:12.671 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:56:18.055 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 13:56:18.056 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:56:18.071 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:56:18.071 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 13:56:31.961 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 13:56:35.973 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 13:56:35.973 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:56:35.984 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:56:35.984 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 13:56:35.993 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "c2x9mUpVMUTYn9f6NjvLJW", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:56:35"}}
2025-07-28 13:56:36.310 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "c2x9mUpVMUTYn9f6NjvLJW", "trace_index": 2, "event_type": "exception", "remarks": "请求处理异常"}
2025-07-28 13:56:36.310 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:31 - traceid:c2x9mUpVMUTYn9f6NjvLJW,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
2025-07-28 13:56:42.606 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "fT4Awic5zAxa6EvSNdLVcU", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:56:42"}}
2025-07-28 13:56:42.664 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "fT4Awic5zAxa6EvSNdLVcU", "trace_index": 2, "event_type": "exception", "remarks": "请求处理异常"}
2025-07-28 13:56:42.665 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:31 - traceid:fT4Awic5zAxa6EvSNdLVcU,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
2025-07-28 13:58:25.345 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "TxbvfbidKqaPpnHj65UAWU", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:58:25"}}
2025-07-28 13:58:52.359 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 13:58:52.359 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 13:58:52.368 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 13:58:52.368 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 13:58:52.483 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "DHN4ufznMgnYAGkyPp78fA", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:58:52"}}
2025-07-28 13:58:52.765 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "DHN4ufznMgnYAGkyPp78fA", "trace_index": 2, "event_type": "exception", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 13:58:52", "error": "division by zero"}, "remarks": "请求处理异常"}
2025-07-28 13:58:52.766 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.exceptions.handle:catch_exceptions_middleware:31 - traceid:DHN4ufznMgnYAGkyPp78fA,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
2025-07-28 14:02:43.223 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 14:02:46.323 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 14:02:46.324 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 14:02:46.335 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 14:02:46.335 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 14:03:05.373 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 14:03:05.375 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 14:03:05.390 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 14:03:05.390 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 14:14:27.602 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 14:14:27.603 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 14:14:27.623 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 14:14:27.623 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 14:14:41.917 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "QK7R9nBGifAv4wsHHgrjJk", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:14:41"}}
2025-07-28 14:14:41.918 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "QK7R9nBGifAv4wsHHgrjJk", "trace_index": 2, "event_type": "exception", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:14:41", "error": ""}, "remarks": "请求处理异常"}
2025-07-28 14:15:44.829 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 14:15:49.854 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 14:15:49.855 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 14:15:49.866 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 14:15:49.866 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 14:15:49.871 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "hsyWWc7BEr3mttcKgnRUoy", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:15:49"}}
2025-07-28 14:15:50.295 | thread_id:********** | thread_name:MainThread | ERROR    | app.core.middlewares.global_exception_middleware:dispatch:29 - traceid:hsyWWc7BEr3mttcKgnRUoy,捕获全局异常：GET URL:http://localhost:9099/v1/system/user/info/current
Headers:{'host': 'localhost:9099', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"macOS"', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:9099/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': 'frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78'}
Exception: ZeroDivisionError: division by zero
2025-07-28 14:15:50.299 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "hsyWWc7BEr3mttcKgnRUoy", "trace_index": 2, "event_type": "response", "msg": {"status_code": 500, "process_time": "0.429s"}}
2025-07-28 14:16:42.137 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
2025-07-28 14:16:46.444 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 14:16:46.444 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 14:16:46.453 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 14:16:46.454 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 14:18:08.671 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:20 - tdumpAdmin启动中...
2025-07-28 14:18:08.672 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:22 - 连接Redis...
2025-07-28 14:18:08.683 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:create_redis_poll:39 - Redis连接成功
2025-07-28 14:18:08.683 | thread_id:********** | thread_name:MainThread | INFO     | app.core.init_app:lifespan:32 - tdumpAdmin启动成功...
2025-07-28 14:18:08.689 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "Hd6t8siuDDgL3ftDC2Qg7s", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:08"}}
2025-07-28 14:18:12.837 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "Hd6t8siuDDgL3ftDC2Qg7s", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "4.149s"}}
2025-07-28 14:18:34.616 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "aLxx6QoL7XztkooviCJRy2", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:34"}}
2025-07-28 14:18:34.674 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "aLxx6QoL7XztkooviCJRy2", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.059s"}}
2025-07-28 14:18:42.920 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "8mboPMoiG3RgoKS2rnsJjF", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:42"}}
2025-07-28 14:18:43.095 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "8mboPMoiG3RgoKS2rnsJjF", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.176s"}}
2025-07-28 14:18:43.884 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "hFinzm7TGUnq2Gre85p79a", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:43"}}
2025-07-28 14:18:43.910 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "hFinzm7TGUnq2Gre85p79a", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.026s"}}
2025-07-28 14:18:44.469 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "biJAPqL5vuhB2jcGPrXsrL", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:44"}}
2025-07-28 14:18:44.527 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "biJAPqL5vuhB2jcGPrXsrL", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.059s"}}
2025-07-28 14:18:45.168 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "M8p4DprZMu3UYy7R4aK3ty", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:45"}}
2025-07-28 14:18:45.198 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "M8p4DprZMu3UYy7R4aK3ty", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.030s"}}
2025-07-28 14:18:45.436 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "XnszEmrg4LbL3bNFTeZPRJ", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:45"}}
2025-07-28 14:18:45.469 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "XnszEmrg4LbL3bNFTeZPRJ", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.034s"}}
2025-07-28 14:18:45.726 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "iUd5pjnAEbxvJw7ZUyu3ok", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:45"}}
2025-07-28 14:18:45.763 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "iUd5pjnAEbxvJw7ZUyu3ok", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.038s"}}
2025-07-28 14:18:45.958 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "j3YqBDeyTDGLkpatJcenKS", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:45"}}
2025-07-28 14:18:45.982 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "j3YqBDeyTDGLkpatJcenKS", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.025s"}}
2025-07-28 14:18:46.112 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "Cf3GqZM2r8yDzv6gszL5BK", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:46"}}
2025-07-28 14:18:46.131 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "Cf3GqZM2r8yDzv6gszL5BK", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.019s"}}
2025-07-28 14:18:46.288 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "YMD85o4CAP4iYLCLgVC6yw", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:46"}}
2025-07-28 14:18:46.307 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "YMD85o4CAP4iYLCLgVC6yw", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.020s"}}
2025-07-28 14:18:46.453 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "hE3kv3tVFdamb7JMR62uF3", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:46"}}
2025-07-28 14:18:46.478 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "hE3kv3tVFdamb7JMR62uF3", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.026s"}}
2025-07-28 14:18:46.607 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "SKAzqyfuEDAAk9GunQe3uu", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:46"}}
2025-07-28 14:18:46.628 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "SKAzqyfuEDAAk9GunQe3uu", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.021s"}}
2025-07-28 14:18:46.772 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "Lv7VSu4p3hr3wk2cVt394T", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:46"}}
2025-07-28 14:18:46.793 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "Lv7VSu4p3hr3wk2cVt394T", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.021s"}}
2025-07-28 14:18:46.966 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "dqZPm5koszUCtAqT589gdz", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:46"}}
2025-07-28 14:18:46.991 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "dqZPm5koszUCtAqT589gdz", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.025s"}}
2025-07-28 14:18:47.093 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "M5nPmirGiAXCQaz4rVPNeP", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:47"}}
2025-07-28 14:18:47.113 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "M5nPmirGiAXCQaz4rVPNeP", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.021s"}}
2025-07-28 14:18:47.284 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "9xx4RvHJ5J4sof285okYsL", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:47"}}
2025-07-28 14:18:47.305 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "9xx4RvHJ5J4sof285okYsL", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.021s"}}
2025-07-28 14:18:47.425 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "fK9Eb4fjpTCSDXTgPLrxTm", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:47"}}
2025-07-28 14:18:47.447 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "fK9Eb4fjpTCSDXTgPLrxTm", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.023s"}}
2025-07-28 14:18:47.630 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "jEhchdCWPARiFnbwbomxkX", "trace_index": 1, "event_type": "request", "msg": {"headers": {"host": "localhost:9099", "connection": "keep-alive", "sec-ch-ua-platform": "\"macOS\"", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlzX3JlZnJlc2giOmZhbHNlLCJleHAiOjE3NTM3NTQ5NDksImlhdCI6MTc1MzY2ODU0OSwibmFtZSI6ImFkbWluIiwic2Vzc2lvbl9pZCI6IjBmMDFmZmFhLWZjMDktNDRmMS04YTM3LTBmNGFjMGU5MTUwMiJ9.wcWBwCFyJTuZAF7Bcycp3L2WNqTXkd9oi5xIHd9YAuI", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:9099/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "frontend_lang=zh_CN; cids=1; _ga=GA1.1.**********.**********; _ga_L7WEXVQCR9=GS2.1.s1749780437$o1$g1$t1749783728$j60$l0$h0; tz=Asia/Shanghai; session_id=2d5f75b8c7d9eb3c25852a35bd80e60252911d78"}, "useragent": {"os": "Mac OS X 10.15.7", "browser": "Chrome 138.0.0", "device": {"family": "Mac", "brand": "Apple", "model": "Mac"}}, "url": "/v1/system/user/info/current", "method": "GET", "ip": "12*******", "params": {}, "ts": "2025-07-28 14:18:47"}}
2025-07-28 14:18:47.666 | thread_id:********** | thread_name:MainThread | INFO     | app.core.middlewares.logger_middleware:async_trace_add_log_record:96 - {"traceid": "jEhchdCWPARiFnbwbomxkX", "trace_index": 2, "event_type": "response", "msg": {"status_code": 200, "process_time": "0.036s"}}
2025-07-28 14:19:07.293 | thread_id:********** | thread_name:MainThread | INFO     | app.core.redis:close_redis_pool:54 - 关闭Redis成功
