# -*- coding: utf-8 -*-
from datetime import datetime
import re

from sqlalchemy import BIGINT, Boolean, DateTime, ForeignKey, MetaData, Text
from sqlalchemy.ext.declarative import as_declarative, declared_attr
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func


@as_declarative()
class Base:
    metadata: MetaData

    id: Mapped[int] = mapped_column(BIGINT, primary_key=True, index=True, unique=True, autoincrement=True, comment='唯一ID')
    create_date: Mapped[datetime] = mapped_column(DateTime, nullable=True, default=datetime.now, server_default=func.now(), comment='创建时间')
    write_date: Mapped[datetime] = mapped_column(DateTime, nullable=True, default=datetime.now, server_default=func.now(), server_onupdate=func.now(), comment='更新时间')
    active: Mapped[bool] = mapped_column(Boolean, default=True, server_default='true', comment='是否归档:True=未删除,False=删除')
    remark: Mapped[str] = mapped_column(Text, nullable=True, default=None, comment='备注')

    def __repr__(self):
        return f'<{self.__class__.__name__}(id={self.id}, active={self.active}, create_date={self.create_date}, write_date={self.write_date})'

    @declared_attr
    def __tablename__(cls) -> str:
        return re.sub(r'(?<!^)(?=[A-Z])', '_', cls.__name__).lower()




class Model(Base):
    __abstract__ = True

    @declared_attr
    def create_uid(cls):
        return mapped_column(BIGINT, ForeignKey('res_user.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True, index=True, comment='创建人')

    @declared_attr
    def write_uid(cls):
        return mapped_column(BIGINT, ForeignKey('res_user.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True, index=True, comment='更新人')

    @declared_attr
    def creator(cls):
        return relationship('ResUserModel', foreign_keys=cls.create_uid, lazy='selectin', uselist=False)

    @declared_attr
    def writer(cls):
        return relationship('ResUserModel', foreign_keys=cls.write_uid, lazy='selectin', uselist=False)
