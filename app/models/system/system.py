# -*- coding: utf-8 -*-
from datetime import datetime

from sqlalchemy import BIGINT, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Model
from app.models.system.m2m import (
    ResRoleMenuModelRel,
    ResRolePermissionsModelRel,
    ResUserRolesModelRel,
)


# todo 将数据范围拆分为一个独立的表
#  新增资源表(菜单、按钮、目录)，权限表，权限表中存储对资源的权限，权限和角色多对多


# todo 权限集成，角色继承
class ResUserModel(Model):
    __tablename__ = 'res_user'
    __table_args__ = (
        {'comment': '系统用户'},
    )

    login: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False, comment='登录名')
    password: Mapped[str] = mapped_column(String(1000), nullable=False, comment='密码')
    # salt: Mapped[str] = mapped_column(String(50), nullable=False, comment='密码盐') -- 不需要，使用passlib已经实现了自动加盐的操作，避免通过暴力破解或通过彩虹表反查
    nickname: Mapped[str] = mapped_column(String(50), nullable=True, index=True, comment='昵称')
    email: Mapped[str] = mapped_column(String(100), nullable=True, index=True, comment='邮箱')
    phone: Mapped[str] = mapped_column(String(20), nullable=True, index=True, comment='手机')
    avatar: Mapped[str] = mapped_column(String(255), nullable=True, comment='头像')
    sex: Mapped[int] = mapped_column(Integer, nullable=True, comment='性别,0:其他,1:男,2:女')
    summary: Mapped[str] = mapped_column(Text, nullable=True, comment='个人简介')

    lang: Mapped[str] = mapped_column(String(50), default='zh_CN', nullable=True, comment='语言')
    tz: Mapped[str] = mapped_column(String(50), default='Asia/Shanghai', nullable=True, comment='时区')
    last_login: Mapped[datetime] = mapped_column(DateTime, nullable=True, comment='最近登录时间')
    login_ip: Mapped[str] = mapped_column(String(128), default='', nullable=True, comment='最后登录IP')

    # is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, server_default='false', nullable=False, comment='是否超管')
    # company_id: Mapped[int] = mapped_column(BIGINT, index=True, nullable=True, comment='公司')
    # company = relationship('ResCompanyModel', primaryjoin='ResUserModel.company_id==foreign(ResCompanyModel.id)',lazy='joined')

    # employee_id: Mapped[int] = mapped_column(ForeignKey('res_employee.id', ondelete='SET NULL'), unique=True, nullable=True, comment='关联员工') # 限制一个用户最多绑定一个员工
    # employee: Mapped['ResEmployeeModel'] = relationship('ResEmployeeModel', back_populates='user', lazy='selectin', uselist=False, foreign_keys=[employee_id])

    roles = relationship('ResRoleModel', secondary=ResUserRolesModelRel.__tablename__, lazy='selectin', uselist=True, foreign_keys='[ResUserRolesModelRel.user_id,ResUserRolesModelRel.role_id]')


class ResPermissionModel(Model):
    __tablename__ = 'res_permission'
    __table_args__ = (
        {'comment': '权限表'},
    )

    code: Mapped[str] = mapped_column(String(32), nullable=False, index=True, unique=True, comment='权限代码')
    name: Mapped[str] = mapped_column(String(32), nullable=False, index=True, unique=True, comment='权限名称')
    path: Mapped[str] = mapped_column(String(255), nullable=True, comment='路径')
    method: Mapped[str] = mapped_column(String(32), nullable=True, comment='方法')
    description: Mapped[str] = mapped_column(Text, nullable=True, comment='描述')
    category: Mapped[str] = mapped_column(String(32), nullable=True, comment='权限类别')

    parent_id: Mapped[int] = mapped_column(ForeignKey('res_permission.id', ondelete='SET NULL'), nullable=True,
                                           index=True, comment='上级权限')
    
    roles = relationship('ResRoleModel', secondary=ResRolePermissionsModelRel.__tablename__, lazy='selectin', back_populates='permissions')


class ResRoleModel(Model):
    __tablename__ = 'res_role'
    __table_args__ = (
        {'comment': '角色表'},
    )

    code: Mapped[str] = mapped_column(String(40), nullable=False, index=True, unique=True, comment='角色代码')
    name: Mapped[str] = mapped_column(String(40), nullable=False, index=True, unique=True, comment='角色名称')
    description: Mapped[str] = mapped_column(Text, nullable=True, comment='描述')
    order: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment='显示排序')
    data_scope: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment='数据权限')

    permissions = relationship('ResPermissionModel', secondary=ResRolePermissionsModelRel.__tablename__, lazy='selectin', back_populates='roles')
    menus = relationship('ResMenuModel', secondary=ResRoleMenuModelRel.__tablename__, lazy='selectin', uselist=True)
    # dept = relationship('ResDepartmentsModel', secondary=ResRoleDeptModelRel.__tablename__, lazy='selectin', uselist=True)


class ResMenuModel(Model):
    __tablename__ = 'res_menu'
    __table_args__ = (
        {'comment': '菜单表'},
    )

    name: Mapped[str] = mapped_column(String(50), nullable=False, index=True, unique=True, comment='名称')
    menu_type: Mapped[str] = mapped_column(String(32), nullable=True, comment='菜单类型')
    icon: Mapped[str] = mapped_column(String(255), nullable=True, comment='图标')
    route_name: Mapped[str | None] = mapped_column(String(50), nullable=True, comment='路由名称')
    route_path: Mapped[str | None] = mapped_column(String(50), nullable=True, comment='路由路径')
    component_path: Mapped[str | None] = mapped_column(String(50), nullable=True, comment='组件路径')
    redirect: Mapped[str | None] = mapped_column(String(50), nullable=True, comment='重定向')
    meta: Mapped[str] = mapped_column(Text, nullable=True, comment='元数据')
    order: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment='显示排序')
    description: Mapped[str | None] = mapped_column(Text, nullable=True, comment='备注')

    parent_id: Mapped[int] = mapped_column(ForeignKey('res_menu.id', ondelete='SET NULL'), nullable=True, index=True,
                                           comment='上级菜单')
    parent: Mapped['ResMenuModel'] = relationship('ResMenuModel',
                                                  primaryjoin='ResMenuModel.parent_id == ResMenuModel.id',
                                                  uselist=False, remote_side='ResMenuModel.id')
    children: Mapped[list['ResMenuModel']] = relationship('ResMenuModel', back_populates='parent',
                                                          cascade='all, delete-orphan', lazy='selectin')


class ResDeptModel(Model):
    __tablename__ = 'res_dept'
    __table_args__ = ({'comment': '部门表'},)

    name: Mapped[str] = mapped_column(String(50), nullable=False, index=True, comment='名称')
    order: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment='排序')

    parent_id: Mapped[int | None] = mapped_column(BIGINT,
                                                  ForeignKey('res_dept.id', ondelete='CASCADE', onupdate='CASCADE'),
                                                  nullable=True, index=True, comment='父级部门ID')
    parent: Mapped['ResDeptModel'] = relationship('ResDeptModel', uselist=False, remote_side='ResDeptModel.id')
    children: Mapped[list['ResDeptModel']] = relationship('ResDeptModel', back_populates='parent',
                                                          cascade='all, delete-orphan', lazy='selectin')

    # company_id: Mapped[int | None] = mapped_column(BIGINT, ForeignKey('res_company.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True, index=True, comment='公司')
    # company: Mapped['ResCompanyModel'] = relationship('ResCompanyModel', back_populates='department_ids', lazy='selectin')

# class ResPartnerModel(Model):
#     __tablename__ = 'res_partner'
#     __table_args__ = (
#         {'comment': '合作伙伴'},
#     )

#     user_id: Mapped[int] = mapped_column(BIGINT, unique=True, comment='用户ID')
#     nick_name: Mapped[str] = mapped_column(String(50), nullable=False, comment='用户昵称')
#     name: Mapped[str] = mapped_column(String(50), nullable=False, unique=True, index=True, comment='名字')
#     display_name: Mapped[str] = mapped_column(String(50), nullable=True, comment='显示名称')
#     company_id: Mapped[int] = mapped_column(ForeignKey('res_company.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True, index=True, comment='公司')
#     partner_type: Mapped[str] = mapped_column(String(50), default='', nullable=True, comment='类型')
#     is_company: Mapped[bool] = mapped_column(Boolean, default=False, nullable=True, server_default='false', comment='是否为公司')


# class ResCompanyModel(Model):
#     __tablename__ = 'res_company'
#     __table_args__ = (
#         {'comment': '公司'},
#     )

#     name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True, comment='名称')
#     address: Mapped[str] = mapped_column(String(255), nullable=True, comment='地址')
#     employee_ids: Mapped[list['ResEmployeeModel']] = relationship('ResEmployeeModel', back_populates='company', lazy='selectin', order_by='ResEmployeeModel.id.asc()')

#     department_ids: Mapped[list['ResDepartmentsModel']] = relationship('ResDepartmentsModel', back_populates='company', lazy='selectin', order_by='ResDepartmentsModel.id.asc()')
#     # users: Mapped[list['ResUserModel']] = relationship('ResUserModel', back_populates='company', primaryjoin='ResCompanyModel.id==foreign(ResUserModel.company_id)', viewonly=True)


# class ResPositionModel(Model):
#     __tablename__ = 'res_position'
#     __table_args__ = (
#         {'comment': '岗位表'},
#     )

#     name: Mapped[str] = mapped_column(String(40), unique=True, nullable=False, index=True, comment='岗位名称')
#     order: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment='显示排序')

#     employees: Mapped[list['ResEmployeeModel']] = relationship('ResEmployeeModel', secondary=ResEmployeePositionsModelRel.__tablename__, overlaps='employees', lazy='selectin', uselist=True,back_populates='positions')


# class ResEmployeeModel(Model):
#     __tablename__ = 'res_employee'
#     __table_args__ = (
#         {'comment': '员工信息'},
#     )

#     name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True, comment='名称')
#     mobile: Mapped[str | None] = mapped_column(String(20), nullable=True, comment='手机号')
#     email: Mapped[str | None] = mapped_column(String(255), nullable=True, comment='邮箱')
#     gender: Mapped[int] = mapped_column(Integer, default=1, nullable=True, comment='性别')
#     address: Mapped[str | None] = mapped_column(String(255), nullable=True, comment='地址')
#     avatar: Mapped[str | None] = mapped_column(String(255), nullable=True, comment='头像', default='/static/avatar/face.png', server_default='/static/avatar/face.png')

#     company_id: Mapped[int | None] = mapped_column(BIGINT, ForeignKey('res_company.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True, index=True, comment='公司')
#     dept_id: Mapped[int] = mapped_column(BIGINT, ForeignKey('res_department.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True, index=True, comment='部门')

#     # 新增 user_id 外键字段
#     # user_id: Mapped[int | None] = mapped_column(BIGINT, ForeignKey('res_user.id', ondelete='SET NULL'), unique=True, nullable=True, comment='用户')

#     company: Mapped['ResCompanyModel'] = relationship('ResCompanyModel', back_populates='employee_ids', lazy='selectin')
#     department: Mapped['ResDepartmentsModel'] = relationship('ResDepartmentsModel', primaryjoin='ResEmployeeModel.dept_id == ResDepartmentsModel.id', lazy='selectin', uselist=False)
#     positions: Mapped[list['ResPositionModel']] = relationship('ResPositionModel', secondary=ResEmployeePositionsModelRel.__tablename__, overlaps='positions', lazy='selectin', uselist=True, back_populates='employees')


#     # 一对一关联 User
#     user: Mapped['ResUserModel'] = relationship('ResUserModel', back_populates='employee', uselist=False, foreign_keys='ResUserModel.employee_id' )
