# -*- coding: utf-8 -*-

from sqlalchemy import BIGINT, Column, ForeignKey

from app.models.base import Base


class ResUserRolesModelRel(Base):
    __tablename__ = 'res_user_role_rel'
    __table_args__ = ({'comment': '用户角色关联表'},)

    user_id = Column(
        BIGINT,
        ForeignKey('res_user.id', ondelete='CASCADE', onupdate='CASCADE'),
        primary_key=True,
        index=True,
        comment='用户ID',
    )
    role_id = Column(
        BIGINT,
        ForeignKey('res_role.id', ondelete='CASCADE', onupdate='CASCADE'),
        primary_key=True,
        index=True,
        comment='角色ID',
    )

    def __repr__(self):
        return f'<{self.__class__.__name__}(user_id={self.user_id}, role_id={self.role_id})'

class ResRolePermissionsModelRel(Base):
    __tablename__ = 'res_role_permissions_rel'
    __table_args__ = ({'comment': '角色权限关系表'},)

    role_id = Column(
        BIGINT,
        ForeignKey('res_role.id', ondelete='CASCADE', onupdate='CASCADE'),
        primary_key=True,
        index=True,
        comment='角色ID',
    )
    permission_id = Column(
        BIGINT,
        ForeignKey('res_permission.id', ondelete='CASCADE', onupdate='CASCADE'),
        primary_key=True,
        index=True,
        comment='权限ID',
    )
    def __repr__(self):
        return f'<{self.__class__.__name__}(role_id={self.role_id}, permission_id={self.permission_id})'


class ResRoleMenuModelRel(Base):
    __tablename__ = 'res_role_menu_rel'
    __table_args__ = ({'comment': '角色菜单关联表'},)

    role_id = Column(
        BIGINT,
        ForeignKey('res_role.id', ondelete='CASCADE', onupdate='CASCADE'),
        primary_key=True,
        index=True,
        comment='角色ID',
    )
    menu_id = Column(
        BIGINT,
        ForeignKey('res_menu.id', ondelete='CASCADE', onupdate='CASCADE'),
        primary_key=True,
        index=True,
        comment='菜单ID',
    )
    def __repr__(self):
        return f'<{self.__class__.__name__}(role_id={self.role_id}, menu_id={self.menu_id})'



#
# class ResRoleDeptModelRel(Base):
#     __tablename__ = 'res_role_depts'
#     __table_args__ = ({'comment': '角色部门关联表'},)
#
#     role_id = Column(
#         BIGINT,
#         ForeignKey('res_role.id', ondelete='CASCADE', onupdate='CASCADE'),
#         primary_key=True,
#         index=True,
#         comment='角色ID',
#     )
#     dept_id = Column(
#         BIGINT,
#         ForeignKey('res_department.id', ondelete='CASCADE', onupdate='CASCADE'),
#         primary_key=True,
#         index=True,
#         comment='部门ID',
#     )
#
#     def __repr__(self):
#         return f'<{self.__class__.__name__}(role_id={self.role_id}, dept_id={self.dept_id})'


# class ResEmployeePositionsModelRel(Base):
#     __tablename__ = 'res_emp_positions'
#     __table_args__ = ({'comment': '员工岗位关联表'},)

#     employee_id = Column(
#         BIGINT,
#         ForeignKey('res_employee.id', ondelete='CASCADE', onupdate='CASCADE'),
#         primary_key=True,
#         index=True,
#         comment='用户ID',
#     )
#     position_id = Column(
#         BIGINT,
#         ForeignKey('res_position.id', ondelete='CASCADE', onupdate='CASCADE'),
#         primary_key=True,
#         index=True,
#         comment='岗位ID',
#     )

#     def __repr__(self):
#         return f'<{self.__class__.__name__}(employee_id={self.employee_id}, position_id={self.position_id})'
