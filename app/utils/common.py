# -*- coding: utf-8 -*-
import base64
import importlib
import inspect
from io import BytesIO
import random
import string
from typing import Dict, List, Optional, Sequence, Tuple, get_type_hints
import uuid

from PIL import Image, ImageDraw, ImageFont

from app.models.base import Model


async def get_captcha_digits_and_ascii(length: int = 4)-><PERSON><PERSON>[str, str]:
    """
    获取验证码图片
    :param length:
    :return:
    """
    total_strings = string.digits + string.ascii_lowercase
    random_strings = random.sample(list(total_strings), length)
    captcha_string = ''.join(random_strings)
    captcha = await generate_captcha(captcha_string)
    captcha_bytes = captcha.getvalue()
    captcha_base64 = base64.b64encode(captcha_bytes).decode()
    return captcha_string,captcha_base64


def get_random_character() -> str:
    """
    获取随机字符
    :return: 随机字符串
    """
    return uuid.uuid4().hex


async def generate_captcha(code) -> BytesIO:
    """
    生成带有噪声和干扰的验证码图片
    https://gitee.com/senqi666/fastapi-vue-admin/blob/master/backend/app/utils/tools.py
    :return: 验证码图片流
    """
    # 创建一张随机颜色背景的图片
    background_color = (random.randint(200, 255), random.randint(200, 255), random.randint(200, 255))
    width, height = 160, 60
    image = Image.new('RGB', (width, height), color=background_color)

    # 获取一个绘图对象
    draw = ImageDraw.Draw(image)

    # 字体设置（如果需要自定义字体，请替换下面的字体路径）
    font = ImageFont.truetype('./app/resources/gantians.otf', 42)

    # 计算验证码文本的总宽度
    total_text_width = 0
    for char in code:
        # 计算文本的宽度
        bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), char, font=font)
        text_width = bbox[2] - bbox[0]
        total_text_width += text_width

    # 计算每个字符的起始位置
    x_offset = (width - total_text_width) / 2
    # 计算文本的高度
    bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), code[0], font=font)
    text_height = bbox[3] - bbox[1]
    y_offset = (height - text_height) / 2 - draw.textbbox((0, 0), code[0], font=font)[1]

    # 绘制每个字符（单独的颜色和扭曲）
    for char in code:
        # 随机选择字体颜色
        text_color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))

        # 计算字符位置并稍微扭曲
        bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), char, font=font)
        char_width = bbox[2] - bbox[0]
        char_x = x_offset + random.uniform(-3, 3)
        char_y = y_offset + random.uniform(-5, 5)

        # 绘制字符
        draw.text((char_x, char_y), char, font=font, fill=text_color)

        # 更新下一个字符的位置
        x_offset += char_width + random.uniform(2, 8)

    # 添加少量的圆圈干扰
    for _ in range(random.randint(2, 4)):
        # 随机位置和大小
        x = random.randint(0, width)
        y = random.randint(0, height)
        radius = random.randint(5, 10)
        draw.ellipse((x - radius, y - radius, x + radius, y + radius), outline=text_color)

    # 添加少量的噪点
    for _ in range(random.randint(10, 20)):
        x = random.randint(0, width - 1)
        y = random.randint(0, height - 1)
        noise_size = random.randint(2, 4)
        noise_color = (random.randint(0, 50), random.randint(0, 50), random.randint(0, 50))
        draw.rectangle([x, y, x + noise_size, y + noise_size], fill=noise_color)

    # 返回验证码图片流
    stream = BytesIO()
    image.save(stream, format='PNG')

    return stream


def get_child_id_map(model_list: Sequence[Model]) -> Dict[int, List[int]]:
    """
    获取子级ID的映射集
    :param model_list: 模型数组
    :return: 映射集字典
    """
    data_map = {}
    for model in model_list:
        data_map.setdefault(model.id, [])
        if model.parent_id:
            data_map.setdefault(model.parent_id, []).append(model.id)

    return data_map


def get_child_recursion(
        id: int,
        id_map: Dict[int, List[int]],
        ids: Optional[List[int]] = None
) -> List[int]:
    """
    递归获取某ID的所有子级的ID数组
    :param id: ID
    :param id_map: ID的映射集
    :param ids: ID数组
    :return: 所有子级的ID数组
    """
    if ids is None:
        ids = []

    ids.append(id)
    child_ids = id_map.get(id, [])
    for child in child_ids:
        get_child_recursion(child, id_map, ids)

    return ids



# def convert_dict_to_search_sql(model:ModelType, search:dict)->List[ColumnElement]:
#     """
#     用于搜索的参数字典转sql表达式列表
#     :param model: 模型对象
#     :param search: 搜索参数
#     :return:
#     """
#     where = []
#     for item in search:
#         field, conditional, value = item
#
#         where_obj = getattr(model, field)
#
#         if conditional == 'like':
#             where.append(where_obj.like(f'%{value}%'))
#
#
#     for key, value in search.items():
#         seq =None
#
#         where_obj = getattr(model, key)
#
#         if seq in ['like']:
#             where.append(where_obj.like(f'%{value}%'))
#
#         if seq == 'in':
#             where.append(where_obj.in_(value))
#
#         if seq == 'between':
#             if not all(value):
#                 continue
#             where.append(where_obj.between(*value))
#
#         if seq is None:
#             where.append(where_obj == value)
#
#     return where





def import_module(module: str):
    module_path, module_class = module.rsplit('.', 1)
    module = importlib.import_module(module_path)
    cls = getattr(module, module_class)
    return cls


def get_endwith_method(clazz, endwith='handler'):
    """
    获取类中指定的endwith结尾的方法名称
    :param clazz:
    :param endwith:
    :return:
    """
    # 获取所有以 "handler" 结尾的方法
    handler_methods = [
        (name, method)
        for name, method in inspect.getmembers(clazz, predicate=inspect.isfunction)
        if name.endswith(endwith)
    ]
    return handler_methods


def extract_param_type(method, param_name='exec'):
    """
    获取方法参数的类型
    :param method:
    :param param_name:
    :return:
    """
    # 获取方法签名
    sig = inspect.signature(method)
    # 获取类型注解（如 `exc: TokenAuthException`）
    type_hints = get_type_hints(method)
    # 返回参数名和类型
    return {
        param.name: type_hints.get(param.name, param.annotation)
        for param in sig.parameters.values()
        if param.name == param_name
    }


def worship():
    print(r"""
////////////////////////////////////////////////////////////////////
//                          _ooOoo_                               //
//                         o8888888o                              //
//                         88" . "88                              //
//                         (| ^_^ |)                              //
//                         O\  =  /O                              //
//                      ____/`---'\____                           //
//                    .'  \\|     |//  `.                         //
//                   /  \\|||  :  |||//  \                        //
//                  /  _||||| -:- |||||-  \                       //
//                  |   | \\\  -  /// |   |                       //
//                  | \_|  ''\---/''  |   |                       //
//                  \  .-\__  `-`  ___/-. /                       //
//                ___`. .'  /--.--\  `. . ___                     //
//              ."" '<  `.___\_<|>_/___.'  >'"".                  //
//            | | :  `- \`.;`\ _ /`;.`/ - ` : | |                 //
//            \  \ `-.   \_ __\ /__ _/   .-` /  /                 //
//      ========`-.____`-.___\_____/___.-`____.-'========         //
//                           `=---='                              //
//      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^        //
//             佛祖保佑       永不宕机      永无BUG                  //
////////////////////////////////////////////////////////////////////
    """)


def alpaca():
    print(r"""
    # + + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + +
    #        ┏┓　　　┏┓+ +
    # 　　　┏┛┻━━━┛┻┓ + +
    # 　　　┃　　　　　　 ┃
    # 　　　┃　　　━　　　┃ ++ + + +
    # 　　 ████━████ ┃+
    # 　　　┃　　　　　　 ┃ +
    # 　　　┃　　　┻　　　┃
    # 　　　┃　　　　　　 ┃ + +
    # 　　　┗━┓　　　┏━┛
    # 　　　　　┃　　　┃
    # 　　　　　┃　　　┃ + + + +
    # 　　　　　┃　　　┃　　　　Codes are far away from bugs with the animal protecting
    # 　　　　　┃　　　┃ + 　　　　神兽保佑,代码无bug
    # 　　　　　┃　　　┃
    # 　　　　　┃　　　┃　　+
    # 　　　　　┃　 　　┗━━━┓ + +
    # 　　　　　┃ 　　　　　　　┣┓
    # 　　　　　┃ 　　　　　　　┏┛
    # 　　　　　┗┓┓┏━┳┓┏┛ + + + +
    # 　　　　　　┃┫┫　┃┫┫
    # 　　　　　　┗┻┛　┗┻┛+ + + +
    # + + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + ++ + + +
    """)
