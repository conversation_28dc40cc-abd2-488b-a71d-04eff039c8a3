# -*- coding: utf-8 -*-
from typing import Any, Dict, Generic, List, Tuple, Type, TypeVar, Union

from mypyc.ir.ops import Sequence
from pydantic import BaseModel
from sqlalchemy import BinaryExpression, Delete, Result, Select, and_, delete, desc, not_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import InstrumentedAttribute, selectinload
from starlette import status

from app.core.exceptions.exception import NotFoundException
from app.models.base import Model

# from app.models.system.system import ResDepartmentsModel
from app.models.system.system import ResUserModel
from app.schemas.system.auth_schema import Auth


ModelType = TypeVar('ModelType', bound=Model)
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)

DomainItem = Union[Tuple[str, str, Any], str]  # 支持 ('field', '=', value) 或 '|'、'&'、'!'

OPERATOR_MAPPING = {
    '=': lambda c, v: c == v,
    '!=': lambda c, v: c != v,
    '<': lambda c, v: c < v,
    '<=': lambda c, v: c <= v,
    '>': lambda c, v: c > v,
    '>=': lambda c, v: c >= v,
    'in': lambda c, v: c.in_(v),
    'not in': lambda c, v: ~c.in_(v),
    'like': lambda c, v: c.like(v),
    'ilike': lambda c, v: c.ilike(v),
}


def convert_domain_expression(model: ModelType, domain: List[DomainItem]) -> BinaryExpression:
    """
    递归解析domain，波兰表达式
    :param model:
    :param domain:
    :return:
    """
    stack = []

    def resolve_condition(condition: Tuple[str, str, Any]):
        field, op, value = condition
        column: InstrumentedAttribute = getattr(model, field, None)
        if not column:
            raise ValueError(f'错误的字段{field}')
        if op not in OPERATOR_MAPPING:
            raise ValueError(f'不支持的操作符{op}')

        return OPERATOR_MAPPING[op](column, value)

    i = 0
    while i < len(domain):
        item = domain[i]

        if item == '&':
            right = stack.pop()
            left = stack.pop()
            stack.append(and_(left, right))

        elif item == '|':
            right = stack.pop()
            left = stack.pop()
            stack.append(or_(left, right))
        elif item == '!':
            operand = stack.pop()
            stack.append(not_(operand))
        else:
            stack.append(resolve_condition(item))

        i += 1

    if len(stack) != 1:
        raise ValueError('domain表达式不正确')

    return stack[0]


def convert_order_by_expression(model: ModelType, order_by: str):
    """
    转换order by 表达式为sqlalchemy的格式
    :param model: 模型对象
    :param order_by: 排序条件，例如按ID 倒序：id desc；按ID顺序排列：id asc或者直接写id；也支持组合排序：例如按创建时间倒序和名字顺序: create_date desc,name asc
    :return:
    """
    order_by_condition = []
    rule_map = {
        'asc': lambda c: c.asc(),
        'desc': lambda c: c.desc()
    }
    order_items = order_by.split(',')
    for item in order_items:
        item = item.strip()
        items = item.split(' ')

        field = 'id'
        rule = 'asc'

        if len(items) == 1:
            field = items[0]
            rule = 'asc'
        elif len(items) == 2:
            field = items[0]
            rule = items[1].lower()

        condition = rule_map[rule](getattr(model, field))
        order_by_condition.append(condition)
    return order_by_condition

def add_sql_options(model: ModelType,sql, sql_options):
    if hasattr(model, 'creator'):
        sql_options.append(selectinload(model.creator))
    if hasattr(model, 'writer'):
        sql_options.append(selectinload(model.writer))

    if sql_options:
        sql_options = list(set(sql_options))
        sql = sql.options(*sql_options)
    return sql

class TdumpDao(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType], auth: Auth):
        """
        Create, Read, Update, Delete (CRUD)
        :param model:  A SQLAlchemy model class
        :param auth:
        """
        self.model = model
        self.auth: Auth = auth
        self.session: AsyncSession = auth.session
        self.current_user: ResUserModel = auth.user

    async def get(self, **kwargs) -> ModelType:
        # == 不会返回TRUE或者False，返回的是一个SQLAlchemy的表达式对象
        columns = [getattr(self.model, key) == value for key, value in kwargs.items() if key != 'sql_options']

        sql = select(self.model).where(*columns).order_by(desc(self.model.id))

        # 异步场景下 处理懒加载的问题
        sql_options:list = kwargs.get('sql_options', [])
        sql = add_sql_options(self.model, sql, sql_options)


        sql = await self.filter_permissions(sql)

        result: Result = await self.session.execute(sql)
        obj = result.scalars().unique().first()
        if not obj:
            raise NotFoundException(
                message='该信息不存在',
                code=status.HTTP_404_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )

        return obj

    async def search(self, domain: List[DomainItem] = None, order_by: str = None, limit: int = None,
                     offset: int = None, sql_options:List=None) -> Sequence[ModelType]:
        """
        搜索数据，使用波兰表达式的方式
        :param domain: 过滤条件:[('field1', '=', value1),('field2', '=', value2)],
        list嵌套三元组的方式，元组之间为 与或 表达式，默认为'&',可为'|',例如[('field1', '=', value1),'|',('field2', '=', value2)]
        元组第一个元素表示模型的字段名称
        元组第二个元素为条件表达式， 支持OPERATOR_MAPPING中的常用表达式: =、!=、<、<=、>、>=、in、not in、like、ilike
        元组第三个元素为查找值。
        示例：
        [('field1', '=', value1),('field2', '=', value2)]

        [('field1', '=', value1),'|',('field2', '=', value2)]
        ['|',('field1', '=', value1),('field2', '=', value2)]
        [('field1', 'not like', value1)]
        [('field1', 'in', [v1,v2,v3])]

        :param order_by: 排序方式， 例如单字段：id desc,组合排序：create_date desc, name asc
        :param limit:
        :param offset:
        :param sql_options: 其他的选项例如：selectinload(ResUserModel.employee)
        :return:
        """
        if not domain:
            domain = []

        expr = convert_domain_expression(self.model, domain)
        sql = select(self.model).where(*expr)

        sql = add_sql_options(self.model, sql, sql_options)

        sql = await self.filter_permissions(sql)

        if order_by:
            sql = sql.order_by(*convert_order_by_expression(self.model, order_by))
        if limit:
            sql = sql.limit(limit)
        if offset:
            sql = sql.offset(offset)

        result: Result = await self.session.execute(sql)
        data = result.scalars().unique().all()
        return data

    # async def list(self, search: List[ColumnElement] = None, order: List[str] = None) -> Sequence[ModelType]:
    #     # offset = (page - 1) * limit
    #
    #     if not search:
    #         search = []
    #     if not order:
    #         order = ['id']
    #     sql = select(self.model).where(*search)
    #     if hasattr(self.model, 'creator'):
    #         sql = sql.options(selectinload(self.model.creator))
    #
    #     sql = await self.filter_permissions(sql)
    #     sql = sql.order_by(*self.get_order_columns(order))
    #
    #     result: Result = await self.session.execute(sql)
    #     data = result.scalars().unique().all()
    #     return data

    async def create(self, obj_in: Union[CreateSchemaType, Dict]) -> ModelType:
        obj_dict = obj_in if isinstance(obj_in, Dict) else obj_in.model_dump()
        obj = self.model(**obj_dict)

        # 添加创建人和更新人信息
        if self.current_user:
            await self.add_creator_and_writer(self.model, 'creator')
            await self.add_creator_and_writer(self.model, 'writer')

        self.session.add(obj)
        await self.session.flush()
        await self.session.refresh(obj)
        return obj

    async def write(self, record_id: int, obj_in: Union[UpdateSchemaType, Dict]) -> ModelType:
        obj_dict = obj_in if isinstance(obj_in, Dict) else obj_in.model_dump(exclude_unset=True, exclude={'id'})
        obj = await self.get(id=record_id)
        for key, value in obj_dict.items():
            setattr(obj, key, value)

            # 添加更新人信息
            if self.current_user:
                await self.add_creator_and_writer(obj, 'writer')

        await self.session.flush()
        await self.session.refresh(obj)
        return obj

    async def delete(self, ids: List[int]) -> None:
        sql: Delete[Any] = delete(self.model).where(self.model.id.in_(ids))
        sql: Delete[Any] = await self.filter_permissions(sql)

        await self.session.execute(sql)
        await self.session.flush()

    async def active(self, record_id: int, active: bool) -> ModelType:
        return await self.write(record_id, {'active': active})

    async def add_creator_and_writer(self, obj, filed):
        if hasattr(obj, filed) and self.current_user:
            rec = await TdumpDao(ResUserModel, self.auth).get(id=self.current_user.id)
            setattr(obj, filed, rec)

    # def get_order_columns(self, order: List[str]) -> List[ColumnElement]:
    #     columns = []
    #     for field in order:
    #         desc_order = False
    #
    #         if field.startswith("-"):
    #             field = field[1:]
    #             desc_order = True
    #
    #         column = getattr(self.model, field)
    #         if desc_order:
    #             column = desc(column)
    #
    #         columns.append(column)
    #
    #     return columns

    async def filter_permissions(self, sql: Union[Select[Any], Delete[Any]]) -> Union[Select[Any], Delete[Any]]:
        return sql
        pass
        # # todo  理解这个函数
        # if not self.current_user or not self.auth.check_data_scope:
        #     return sql
        #
        # if not hasattr(self.model, 'creator'):
        #     return sql
        #
        # if self.current_user.is_superuser:
        #     return sql
        #
        # if not self.current_user.employee.dept_id or not self.current_user.roles:
        #     return sql.where(self.model.create_uid == self.current_user.id)
        #
        # data_scope = set()
        # dept_ids = set()
        #
        # for role in self.current_user.roles:
        #     for dept in role.depts:
        #         dept_ids.add(dept.id)
        #     data_scope.add(role.data_scope)
        #
        # if 1 in data_scope:
        #     return sql.where(self.model.create_uid == self.current_user.id)
        #
        # if 2 in data_scope:
        #     dept_ids.add(self.current_user.employee.dept_id)
        #
        # if 3 in data_scope:
        #     from app.utils.common import get_child_id_map, get_child_recursion
        #     dept_objs = await TdumpDao(ResDepartmentsModel, self.auth).list()
        #     id_map = get_child_id_map(dept_objs)
        #     dept_child_ids = get_child_recursion(id=self.current_user.employee.dept_id, id_map=id_map)
        #     for child_id in dept_child_ids:
        #         dept_ids.add(child_id)
        # if 4 in data_scope:
        #     return sql
        #
        # return sql.where(self.model.creator.has(ResUserModel.employee.dept_id.in_(list(dept_ids))))
