# -*- coding: utf-8 -*-
from typing import Sequence

from app.crud.base import TdumpDao
from app.models.system.system import ResRoleModel
from app.schemas.system.role_schema import RoleCreate, RoleUpdate


class RoleDao(TdumpDao[ResRoleModel, RoleCreate, RoleUpdate]):
    def __init__(self, auth):
        self.auth = auth
        super().__init__(ResRoleModel, auth)

    async def get_by_user_id(self, user_id:int)-> Sequence[ResRoleModel]:
        roles = await self.search(domain=[('id', '=', 1)])
        # todo
        return roles
