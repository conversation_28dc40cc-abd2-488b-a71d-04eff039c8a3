# -*- coding: utf-8 -*-
import datetime

from app.crud.base import Tdump<PERSON><PERSON>
from app.models.system.system import ResUser<PERSON>odel
from app.schemas.system.auth_schema import Auth
from app.schemas.system.user_schema import UserCreate, UserUpdate


class UserDao(TdumpDao[ResUserModel, UserCreate, UserUpdate]):

    def __init__(self, auth: Auth):
        self.auth = auth
        super().__init__(ResUserModel, auth=auth)

    async def get_by_login(self, login) -> ResUserModel:
        return await self.get(login=login)

    async def get_by_id(self, user_id: int) -> ResUserModel:
        return await self.get(id=user_id)

    async def update_user_password(self, user_id, hashed_password):
        return await self.write(record_id=user_id, obj_in={'password': hashed_password})

    async def update_last_login_info(self, user_id: int, ip: str):
        update_dict = {
            'last_login': datetime.datetime.now(),  # todo 时区问题？
            'login_ip': ip
        }
        return await self.write(record_id=user_id, obj_in=update_dict)
