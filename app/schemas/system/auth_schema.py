# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional, Union

from pydantic import BaseModel, ConfigDict
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.system.user_schema import UserPermissionOut


class AuthUser(UserPermissionOut):
    ...

class Auth(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    user: Optional[AuthUser] = None
    check_data_scope: bool = False
    session: AsyncSession


class JwtPayload(BaseModel):
    sub: str
    is_refresh: bool
    exp: Union[datetime, int]


class RefreshTokenPayload(BaseModel):
    refresh_token: str

class JwtOut(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = 'Bearer'
    expires_in: int

class CaptchaOut(BaseModel):
    key: str
    b64_image: str
