# -*- coding: utf-8 -*-
from typing import Optional

from pydantic import BaseModel

from app.schemas.base import TdumpCreateBase, TdumpOutBaseInfoSchema, TdumpOutBaseSchema, TdumpUpdateBase
from app.schemas.system.company_schema import CompanyIdName


class DepartmentIdName(TdumpOutBaseSchema):
    pass


class DepartmentBase(BaseModel):
    name: str
    order: Optional[int] = None
    parent_id: Optional[int] = None


class DepartmentCreate(DepartmentBase, TdumpCreateBase):
    pass


class DepartmentUpdate(DepartmentBase, TdumpUpdateBase):
    pass


# todo 这里有层级结构，需要考虑如何返回
class DepartmentOut(DepartmentBase, TdumpOutBaseInfoSchema):
    company: Optional[CompanyIdName] = None
    parent: Optional[DepartmentIdName] = None
