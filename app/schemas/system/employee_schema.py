# -*- coding: utf-8 -*-
from typing import List, Optional

from pydantic import BaseModel, ConfigDict, field_validator

from app.core.validator import mobile_validator
from app.schemas.base import TdumpCreateBase, TdumpOutBaseInfoSchema, TdumpOutBaseSchema, TdumpUpdateBase
from app.schemas.system.company_schema import CompanyIdName
from app.schemas.system.department_schema import DepartmentIdName
from app.schemas.system.position_schema import PositionIdName


class EmployeeRel(BaseModel):
    company: Optional[CompanyIdName] = None
    dept: Optional[DepartmentIdName] = None
    position: Optional[List[PositionIdName]] = None


class EmployeeBase(BaseModel):
    name: str
    mobile: str
    email: str
    gender: Optional[int] = 1
    address: Optional[str] = None
    avatar: Optional[str] = None

    user_id: Optional[int] = None


class EmployeeResUserOut(TdumpOutBaseInfoSchema, EmployeeBase, EmployeeRel):
    model_config = ConfigDict(from_attributes=True)


class EmployeeCreate(EmployeeBase, TdumpCreateBase):
    company_id: int
    position_ids: List[int] = []

    @classmethod
    @field_validator('mobile')
    def validate_mobile(cls, value: str):
        return mobile_validator(value)

    @classmethod
    @field_validator('email')
    def validate_email(cls, value: str):
        # todo
        return value


class EmployeeUpdate(TdumpUpdateBase, EmployeeCreate):
    pass


class EmployeeSimpleOut(EmployeeResUserOut):
    model_config = ConfigDict(from_attributes=True)


class EmployeeIdName(TdumpOutBaseSchema):
    pass
