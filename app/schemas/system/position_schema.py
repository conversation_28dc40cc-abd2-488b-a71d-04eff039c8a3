# -*- coding: utf-8 -*-
from typing import Optional

from pydantic import BaseModel, ConfigDict

from app.schemas.base import TdumpCreateBase, TdumpOutBaseInfoSchema, TdumpOutBaseSchema, TdumpUpdateBase


class PositionBase(BaseModel):
    name: str
    order: Optional[int] = 1


class PositionCreate(PositionBase, TdumpCreateBase):
    pass


class PositionUpdate(PositionBase, TdumpUpdateBase):
    pass


class PositionOut(TdumpOutBaseInfoSchema, PositionBase):
    model_config = ConfigDict(from_attributes=True)


class PositionIdName(TdumpOutBaseSchema):
    pass
