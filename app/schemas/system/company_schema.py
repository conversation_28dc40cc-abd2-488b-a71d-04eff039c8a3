# -*- coding: utf-8 -*-
from pydantic import BaseModel

from app.schemas.base import TdumpCreateBase, TdumpOutBaseInfoSchema, TdumpOutBaseSchema, TdumpUpdateBase


class CompanyBase(BaseModel):
    name: str
    address: str


class CompanyCreate(CompanyBase, TdumpCreateBase):
    pass


class CompanyUpdate(CompanyBase, TdumpUpdateBase):
    pass


class CompanyOut(TdumpOutBaseInfoSchema, CompanyBase):
    pass


class CompanyIdName(TdumpOutBaseSchema):
    pass
