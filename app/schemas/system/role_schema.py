# -*- coding: utf-8 -*-
from typing import List, Optional

from pydantic import BaseModel, ConfigDict

from app.schemas.base import TdumpCreateBase, TdumpOutBaseInfoSchema, TdumpOutBaseSchema, TdumpUpdateBase
from app.schemas.system.department_schema import DepartmentIdName
from app.schemas.system.menus_schema import MenusIdName


class RoleIdName(TdumpOutBaseSchema):
    pass


class RoleBase(BaseModel):
    name: str
    order: Optional[int] = None
    data_scope: int


class RoleCreate(RoleBase, TdumpCreateBase):
    pass


class RoleUpdate(RoleBase, TdumpUpdateBase):
    pass


class RoleOut(RoleBase, TdumpOutBaseInfoSchema):
    model_config = ConfigDict(from_attributes=True)
    menus: List[MenusIdName]
    depts: List[DepartmentIdName]
