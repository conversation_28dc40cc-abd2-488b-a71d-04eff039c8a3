# -*- coding: utf-8 -*-
from typing import List, Optional

from pydantic import BaseModel, ConfigDict, Field

from app.core.validator import DateTimeStr
from app.schemas.base import TdumpBaseActiveSchema, TdumpCreateBase, TdumpOutBaseInfoSchema, TdumpUpdateBase
from app.schemas.system.company_schema import CompanyIdName
from app.schemas.system.employee_schema import EmployeeResUserOut
from app.schemas.system.role_schema import RoleOut


class UserRel(BaseModel):
    company: Optional[CompanyIdName] = None

class UserBase(TdumpBaseActiveSchema):
    login: str
    nickname: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    avatar: Optional[str] = None
    sex: Optional[int] = Field(0, description='性别')
    summary: Optional[str] = None

    lang: Optional[str] = None
    tz: Optional[str] = None


class UserCreate(UserBase,TdumpCreateBase):
    password: Optional[int] = Field('123456', description='密码')
    role_ids: List[int] = []

    # company_id: Optional[int]=None
    # employee_id: Optional[int] = None


class UserUpdate(UserBase,TdumpUpdateBase):
    pass
    # password: Optional[int] = None

    # employee_id: Optional[int] = None
    # company_id: Optional[int]=None


class CurrentUserPasswordChange(BaseModel):
    old_password: str
    new_password: str


class UserBatchSetAvailable(BaseModel):
    ids: List[int] = []


class UserRolesSetting(BaseModel):
    user_ids: List[int] = []
    role_ids: List[int] = []


class UserOut(TdumpOutBaseInfoSchema, UserBase, UserRel):
    model_config = ConfigDict(from_attributes=True)

    last_login: Optional[DateTimeStr] = None
    login_ip: Optional[str] = None

    employee: Optional[EmployeeResUserOut] = None


class UserPermissionOut(UserOut):
    model_config = ConfigDict(from_attributes=True)

    roles: Optional[List[RoleOut]] = None

