# -*- coding: utf-8 -*-
from typing import Optional

from pydantic import BaseModel, ConfigDict

from app.schemas.base import Tdump<PERSON>reateBase, TdumpOutBaseInfoSchema, TdumpOutBaseSchema, TdumpUpdateBase


class MenusIdName(TdumpOutBaseSchema):
    pass


class MenusBase(BaseModel):
    name: str
    type: str
    icon: str
    order: Optional[int] = 1
    permission: str
    route_name: Optional[str] = None
    route_path: Optional[str] = None
    component_path: Optional[str] = None
    redirect: Optional[str] = None
    cache: Optional[bool] = None
    hidden: Optional[bool] = None
    description: Optional[str] = None


class MenusCreate(MenusBase, TdumpCreateBase):
    pass


class MenusUpdate(MenusBase, TdumpUpdateBase):
    pass


# todo 这里有层级结构，需要考虑如何返回
class MenusOut(MenusBase, TdumpOutBaseInfoSchema):
    model_config = ConfigDict(from_attributes=True)
    parent: Optional[MenusIdName] = None
