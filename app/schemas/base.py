# -*- coding: utf-8 -*-
from typing import Optional

from pydantic import BaseModel, ConfigDict

from app.core.validator import DateTimeStr


class TdumpBaseActiveSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    active: Optional[bool] = True


class TdumpRemark(BaseModel):
    remark: Optional[str] = None


class TdumpOutBaseSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int = None
    name: str = None


class TdumpOutBaseInfoSchema(TdumpBaseActiveSchema, TdumpRemark):
    model_config = ConfigDict(from_attributes=True)

    id: int
    creator: Optional[TdumpOutBaseSchema] = None
    writer: Optional[TdumpOutBaseSchema] = None
    create_date: Optional[DateTimeStr] = None
    write_date: Optional[DateTimeStr] = None


class TdumpUpdateBase(TdumpBaseActiveSchema, TdumpRemark):
    id: int


class TdumpCreateBase(TdumpBaseActiveSchema, TdumpRemark):
    pass
