# -*- coding: utf-8 -*-
from typing import Dict

from app.crud.system.crud_user import UserDao
from app.schemas.system.auth_schema import Auth
from app.schemas.system.user_schema import UserOut, UserPermissionOut


class UserService:
    @classmethod
    async def get_detail_by_login(cls, login: str, auth: Auth) -> UserPermissionOut:
        user = await UserDao(auth).get_by_login(login)
        # roles = await RoleDao(auth).get_by_user_id(user.id)
        # user.roles = roles

        # todo 需要考虑一下是否有必要返回 用户关联的所有的信息
        return UserPermissionOut.model_validate(user)

    @classmethod
    async def get_current_user_info(cls, auth: Auth) -> Dict:
        user = await UserDao(auth).get_by_id(auth.user.id)
        data = UserOut.model_validate(user)

        data = data.model_dump()
        # todo 返回菜单数据

        return data

    @classmethod
    async def update_current_user_avatar(cls, auth):
        pass

    @classmethod
    async def update_current_user_password(cls, auth):
        pass

    @classmethod
    async def get_user_list(cls, page_query, user_query, auth):
        pass

    @classmethod
    async def get_detail_by_id(cls, user_id, auth):
        pass

    @classmethod
    async def create_user(cls, user_form, auth):
        pass

    @classmethod
    async def update_user(cls, user_id, user_form, auth):
        pass

    @classmethod
    async def delete_user(cls, user_id, auth):
        pass

    @classmethod
    async def assign_roles(cls, user_role, auth):
        pass

    @classmethod
    async def get_user_roles(cls, user_id, auth):
        pass

    @classmethod
    async def enable_user(cls, data, auth):
        pass

    @classmethod
    async def disable_user(cls, data, auth):
        pass
