# -*- coding: utf-8 -*-
import subprocess
import time


class Upgrade:

    def execute(self, command):
        result = subprocess.run(
            command,
            capture_output=True,
            text=True
        )
        print(result.stdout)
        print(result.stderr)

    def upgrade_table(self, message) -> None:
        """

        :return:
        """
        if not message:
            message = str(int(time.time()))
        command_list = [
            ['alembic', 'revision', '--autogenerate', '-m', f'"{message}"'],
            ['alembic', 'upgrade', 'head']
        ]
        for command in command_list:
            self.execute(command)

    def downgrade(self, revision) -> None:
        command = ['alembic', 'downgrade', f'{revision}']
        self.execute(command)


#  # todo 从路由上拿到route_name中配置的权限码，然后和数据库中做比对
# from sqlalchemy import select
# from sqlalchemy.ext.asyncio import AsyncSession
# from .models import Permission
#
# async def sync_permissions_from_routes(app, db: AsyncSession):
#     """
#     从 FastAPI 路由扫描权限信息并同步到数据库
#     """
#     result = await db.execute(select(Permission))
#     existing_permissions = {p.code: p for p in result.scalars().all()}
#
#     for route in app.routes:
#         if getattr(route, "name", None) and route.name != route.path:
#             code = route.name
#             name = route.summary or code  # summary 作为权限名称
#             if code in existing_permissions:
#                 if existing_permissions[code].name != name:
#                     existing_permissions[code].name = name
#                     print(f"✅ 更新权限名称: {code} -> {name}")
#             else:
#                 db.add(Permission(code=code, name=name))
#                 print(f"➕ 新增权限: {code} -> {name}")
#
#     await db.commit()
