# -*- coding: utf-8 -*-
from pathlib import Path
from typing import Dict, List, TypeVar

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy_utils import create_database, database_exists

from app.core.config import app_settings, db_settings
from app.models.base import Model


ModelType = TypeVar('ModelType', bound=Model)


class InitializeData:
    """
    初始化数据
    """
    SCRIPT_DIR: Path = Path.joinpath(app_settings.APP_BASE_DIR, 'scripts', 'initialize')

    def __init__(self) -> None:
        self.engine = create_engine(url=db_settings.get_db_url(synchronous=True),
                                    **db_settings.get_engine_params()
                                    )

        self.db_session = sessionmaker(bind=self.engine)

    def init_database(self):
        # 如果数据库不存在则自动创建
        try:
            if not database_exists(self.engine.url):
                create_database(self.engine.url)
        except Exception as e:
            raise e

    def init_data(self) -> None:
        """
        初始化数据
        :return:
        """
        pass

    def read_init_from_json(self, filename) -> List[Dict]:
        pass

    def update_sequence(self, model: ModelType, max_rows: int) -> None:
        """
        更新表最大ID
        :param model:
        :param max_rows:
        :return:
        """
        pass

    def run(self) -> None:
        """
        执行初始化操作
        :return:
        """
        self.init_database()
        self.init_data()
