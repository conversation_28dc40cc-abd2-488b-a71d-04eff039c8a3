# 管理系统基础模版

UV(项目管理工具) + ruff(代码格式化) + mypy(代码静态检查)

## 数据库升级工具

### 1.安装并初始化

```shell
uv add alembic
# 初始化仓库 创建一个名叫alembic的仓库
alembic init alembic
```

### 2.修改数据库连接配置(此步骤可忽略-已经调整env.py文件默认使用配置文件中的数据库连接)

在alembic.ini中设置数据库的连接

例如： `sqlalchemy.url = postgresql://test:testpwd@127.0.0.1:5432/msfwx`

### 3.修改模型元类

```python
# 将当前项目的路径导入到path
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(f'alembic的BASE DIR:{BASE_DIR}')
sys.path.insert(0, BASE_DIR)
# 设置创建模型的元类
from app.models.base_model import ModelBase
from app.core.database import db_url as url

target_metadata = ModelBase.metadata
```

### 4.生成迁移文件

`alembic revision --autogenerate -m "message"` 将当前模型中的状态生成迁移文件

### 5.将迁移文件映射到数据库中

使用 `alembic upgrade head` 将刚刚生成的迁移文件(head 表示最新的版本号)
，真正映射到数据库中；同理，如果要降级，那么使用 `alembic downgrade 版本号码`

### 修改了表结构重复步骤4、步骤5

### 生成离线更新脚本

在某些不适合在线更新的情况，可以采用生成sql脚本的形式进行离线更新

```shell
alembic upgrade <version> --sql > migration.sql
alembic upgrade ae1027a6acf --sql > migration.sql

# 从特定起始版本生成sql脚本
alembic upgrade <vsersion>:<vsersion> --sql > migration.sql
alembic upgrade 1975ea83b712:ae1027a6acf --sql > migration.sql
```
