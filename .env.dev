# -------- 应用配置 --------
# 应用运行环境
APP_ENV=dev
APP_DEMO=false
# 应用名称
APP_NAME=tdumpAdmin
# 应用描述
APP_DESCRIPTION=一个后台管理系统系统基础框架
# 应用代理路径
APP_API_PREFIX=
# 应用主机
APP_SERVER_HOST=0.0.0.0
# 应用端口
APP_SERVER_PORT=9099
# 应用版本
APP_VERSION=1.0.0
# 应用是否开启热重载
APP_RELOAD=true
# 应用是否开启IP归属区域查询
APP_IP_LOCATION_QUERY=true
# 应用是否允许账号同时登录
APP_SAME_TIME_LOGIN=true

# 文档地址 默认为docs
APP_DOCS_URL=/docs
# 文档关联请求数据接口
APP_OPENAPI_URL=/openapi.json
# redoc 文档
APP_REDOC_URL=/redoc

APP_CACHES_DIR='caches'

# ================================================= #
# ***************** 静态文件目录配置 ***************** #
# ================================================= #
# 是否启用静态文件目录访问
APP_STATIC_ENABLE=True
# 路由访问
APP_STATIC_URL=/static
# 静态文件目录名
APP_STATIC_DIR=static

# ================================================= #
# ***************** 临时文件目录配置 ***************** #
# ================================================= #
# 是否启用临时文件目录访问
APP_TEMP_ENABLE=false
# 路由访问
APP_TEMP_URL=/temp
# 临时文件目录名
APP_TEMP_DIR=temp

# ================================================= #
# ********************* 日志配置 ******************* #
# ================================================= #
# 是否开启每次操作日志记录到数据库
APP_OPERATION_LOG_RECORD=true
## 只记录包括的请求方式记录到数据库
#APP_OPERATION_RECORD_METHOD=["POST", "PUT", "PATCH", "DELETE"]
## 忽略的操作接口函数名称，列表中的函数名称不会被记录到操作日志中
#APP_IGNORE_OPERATION_FUNCTION=["get_captcha_for_login"]

# ================================================= #
# ******************** 跨域配置 ******************** #
# ================================================= #
# 是否启用跨域
APP_CORS_ORIGIN_ENABLE=true
# 只允许访问的域名列表, * 代表所有
APP_ALLOW_ORIGINS=["*"]
# 允许跨域的http方法, 例如 get、post、put 等
APP_ALLOW_METHODS=["*"]
# 允许携带的headers, 可以用来鉴别来源等
APP_ALLOW_HEADERS=["*"]
# 是否支持携带 cookie
APP_ALLOW_CREDENTIALS=True


# ================================================= #
# ******************* 登录认证配置 ****************** #
# ================================================= #
# 生产环境保管好 token的SECRET_KEY
JWT_SECRET_KEY='b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55'
# 生成token的加密算法
JWT_ALGORITHM="HS256"
# access_token 过期时间：60 * 24
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440
# refresh_token 过期时间：60 * 24 * 7
JWT_REFRESH_TOKEN_EXPIRE_MINUTES=10080
# jwt token 过期时间
JWT_REDIS_EXPIRE_MINUTES=30

# ================================================= #
# ******************** 验证码配置 ******************* #
# ================================================= #
# 是否开启登录验证码功能
CAPTCHA_ENABLE=true
# 验证码过期时间
CAPTCHA_EXPIRE_SECONDS=60

# ================================================= #
# ******************** 数据库配置 ******************* #
# ================================================= #
# 数据库类型，可选的有'postgresql'、'sqlite3','mysql','mssql',，默认为'postgresql'
DB_TYPE='postgresql'
DB_USERNAME='odoo'
DB_PASSWORD='Drag0n@190916'
DB_HOST="localhost"
DB_PORT=5483
DB_DATABASE='tdumpAdmin'
# 数据库连接的可选参数
#DB_QUERY={"sslmode": "disable"}
# 设置是否输出执行的SQL具体信息 是否开启sqlalchemy日志
DB_ECHO=true
# 允许溢出连接池大小的最大连接数
DB_MAX_OVERFLOW=10
# 连接池大小，0表示连接数无限制
DB_POOL_SIZE=50
# 连接回收时间（单位：秒）
DB_POOL_RECYCLE=3600
# 连接池中没有线程可用时，最多等待的时间（单位：秒）
DB_POOL_TIMEOUT=30


# ================================================= #
# ******************** Redis配置 ******************* #
# ================================================= #
REDIS_ENABLE=True
# REDIS_URL: RedisDsn = "redis://127.0.0.1:6379/0"
REDIS_HOST='127.0.0.1'
REDIS_PORT=6379
REDIS_USERNAME=''
REDIS_PASSWORD=''
REDIS_DATABASE=0

# ================================================= #
# ***************** 文件上传配置 ******************** #
# ================================================= #
# 文件上传路径
UPLOAD_PATH='/static/upload'
# 文件上传大小限制:5 * 1024 * 1024
UPLOAD_SIZE=5242880
DEFAULT_ALLOWED_EXTENSION='["bmp",  "gif", "jpg", "jpeg", "png", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "html", "htm", "txt", "rar", "zip", "gz", "bz2", "mp4", "avi", "rmvb", "pdf"]'

# ================================================= #
# ***************** Minio配置 ********************** #
# ================================================= #

