# FastAPI + SQLAlchemy + Alembic 权限系统项目结构（Odoo 风格）

来自ChatGpt

https://chatgpt.com/share/6888dcb0-1e50-800d-9775-0f48e9ecb18b

fastapi_rbac/
├── app/
│   ├── main.py                     # FastAPI 入口
│   ├── db/
│   │   ├── base.py                 # Base类
│   │   ├── models/
│   │   │   ├── user.py             # 用户模型
│   │   │   ├── group.py            # 用户组模型
│   │   │   ├── model_access.py     # 模型访问权限 (ACL)
│   │   │   ├── record_rule.py      # 记录规则 (ABAC)
│   │   │   ├── sale_order.py       # 示例业务模型
│   ├── schemas/
│   │   ├── user.py
│   │   ├── sale_order.py
│   ├── services/
│   │   ├── access_control.py       # 权限控制逻辑（装饰器、规则等）
│   ├── api/
│   │   ├── routes/
│   │   │   ├── sale_order.py       # 带权限的业务路由
│   ├── core/
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── security.py
│   ├── scripts/
│   │   ├── init_data.py            # 初始化用户、组、ACL、Rule 的脚本
├── alembic/
│   ├── versions/
│   ├── env.py
│   └── script.py.mako
├── alembic.ini
├── requirements.txt


✅ 示例模型说明

1. 用户、用户组（user.py / group.py）
```python
# user.py
class User(Base):
    __tablename__ = 'res_users'
    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True)
    is_superuser = Column(Boolean, default=False)
    groups = relationship("Group", secondary="res_users_groups", back_populates="users")

# group.py
class Group(Base):
    __tablename__ = 'res_groups'
    id = Column(Integer, primary_key=True)
    name = Column(String)
    users = relationship("User", secondary="res_users_groups", back_populates="groups")
    acls = relationship("ModelAccess", back_populates="group")
    rules = relationship("RecordRule", back_populates="group")
```
2. 模型访问权限（model_access.py）
```python
class ModelAccess(Base):
    __tablename__ = "ir_model_access"
    id = Column(Integer, primary_key=True)
    model_name = Column(String)
    group_id = Column(Integer, ForeignKey("res_groups.id"))
    group = relationship("Group", back_populates="acls")
    can_read = Column(Boolean, default=False)
    can_write = Column(Boolean, default=False)
    can_create = Column(Boolean, default=False)
    can_delete = Column(Boolean, default=False)
```
3. 记录规则（record_rule.py）
```python
class RecordRule(Base):
    __tablename__ = "ir_rule"
    id = Column(Integer, primary_key=True)
    model_name = Column(String)
    domain = Column(String)  # domain: e.g. "SaleOrder.user_id == user.id"
    group_id = Column(Integer, ForeignKey("res_groups.id"))
    group = relationship("Group", back_populates="rules")
    perm_read = Column(Boolean, default=True)
    perm_write = Column(Boolean, default=False)
    perm_create = Column(Boolean, default=False)
    perm_unlink = Column(Boolean, default=False)
```

4. 示例业务模型：销售订单（sale_order.py）
```python
class SaleOrder(Base):
    __tablename__ = "sale_order"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    user_id = Column(Integer, ForeignKey("res_users.id"))
    amount = Column(Float)
    user = relationship("User")
```

5. 权限控制逻辑（services/access_control.py）
```python
def check_model_access(user: User, model: str, action: str) -> bool:
    if user.is_superuser:
        return True
    for group in user.groups:
        for acl in group.acls:
            if acl.model_name == model and getattr(acl, f"can_{action}"):
                return True
    return False

def apply_record_rules(user: User, model: str, query):
    if user.is_superuser:
        return query
    for group in user.groups:
        for rule in group.rules:
            if rule.model_name == model and rule.perm_read:
                domain = eval(rule.domain, {"user": user})
                query = query.filter(domain)
    return query
```
6. 初始化权限脚本（scripts/init_data.py）
```python
def init_permission_data(session):
    group_sales = Group(name="Salesman")
    group_manager = Group(name="Sales Manager")

    session.add_all([group_sales, group_manager])
    session.flush()

    session.add(ModelAccess(
        model_name="SaleOrder",
        group_id=group_sales.id,
        can_read=True, can_create=True
    ))

    session.add(RecordRule(
        model_name="SaleOrder",
        domain="SaleOrder.user_id == user.id",
        group_id=group_sales.id,
        perm_read=True
    ))

    session.commit()
```

7. 示例：菜单权限控制逻辑（models/menu.py）

```python
class Menu(Base):
    __tablename__ = "ir_ui_menu"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    parent_id = Column(Integer, ForeignKey("ir_ui_menu.id"), nullable=True)
    parent = relationship("Menu", remote_side=[id])
    groups = relationship("Group", secondary="menu_groups", back_populates="menus")
```

⚙ 示例：菜单过滤逻辑（services/access_control.py）

```python
def get_user_menus(user: User, db: Session):
    if user.is_superuser:
        return db.query(Menu).all()
    group_ids = [g.id for g in user.groups]
    return db.query(Menu).join(Menu.groups).filter(Group.id.in_(group_ids)).all()
```