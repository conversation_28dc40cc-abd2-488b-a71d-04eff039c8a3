# 使用fastapi+sqlalchemy2.0+实现一个RBAC权限管理系统，需要支持记录规则和字段权限

来自ChatGpt

https://chatgpt.com/share/688b813f-0bfc-800d-8f9c-eedc3d714b70

🧱 基础设计模块

📌 1. 用户、角色、权限结构
```python
# models/user.py
class User(Base):
    __tablename__ = "users"
    id: Mapped[int] = mapped_column(primary_key=True)
    username: Mapped[str] = mapped_column(unique=True)
    roles: Mapped[List["Role"]] = relationship(
        secondary="user_roles", back_populates="users"
    )

class Role(Base):
    __tablename__ = "roles"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(unique=True)
    users: Mapped[List["User"]] = relationship(
        secondary="user_roles", back_populates="roles"
    )
    permissions: Mapped[List["Permission"]] = relationship(
        secondary="role_permissions", back_populates="roles"
    )

user_roles = Table(
    "user_roles", Base.metadata,
    Column("user_id", ForeignKey("users.id")),
    Column("role_id", ForeignKey("roles.id")),
)

role_permissions = Table(
    "role_permissions", Base.metadata,
    Column("role_id", ForeignKey("roles.id")),
    Column("permission_id", ForeignKey("permissions.id")),
)
```

📌 2. 权限、字段权限、记录规则
```python
# models/permission.py
class Permission(Base):
    __tablename__ = "permissions"
    id: Mapped[int] = mapped_column(primary_key=True)
    resource: Mapped[str]  # e.g., "User", "Customer"
    action: Mapped[str]    # e.g., "read", "write", "delete"
    roles: Mapped[List["Role"]] = relationship(
        secondary="role_permissions", back_populates="permissions"
    )
    field_permissions: Mapped[List["FieldPermission"]] = relationship(back_populates="permission")
    row_rules: Mapped[List["RowLevelRule"]] = relationship(back_populates="permission")

class FieldPermission(Base):
    __tablename__ = "field_permissions"
    id: Mapped[int] = mapped_column(primary_key=True)
    permission_id: Mapped[int] = mapped_column(ForeignKey("permissions.id"))
    field_name: Mapped[str]  # e.g., "email"
    can_read: Mapped[bool]
    can_write: Mapped[bool]
    permission: Mapped["Permission"] = relationship(back_populates="field_permissions")

class RowLevelRule(Base):
    __tablename__ = "row_level_rules"
    id: Mapped[int] = mapped_column(primary_key=True)
    permission_id: Mapped[int] = mapped_column(ForeignKey("permissions.id"))
    condition: Mapped[str]  # e.g., "owner_id = :user_id"
    permission: Mapped["Permission"] = relationship(back_populates="row_rules")
```

⚙️ 权限检查依赖
```python
# core/security.py
from fastapi import Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from models import User, Permission

async def get_current_user(...):
    # JWT 解码或 session 获取
    ...

async def has_permission(
    resource: str,
    action: str,
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_current_user)
):
    stmt = select(Permission).join(Role.permissions).join(User.roles).where(
        User.id == user.id,
        Permission.resource == resource,
        Permission.action == action
    )
    result = await session.execute(stmt)
    permission = result.scalar_one_or_none()
    if not permission:
        raise HTTPException(status_code=403, detail="Permission denied")
    return permission
```
🚀 应用示例：查询客户数据时限制行与字段

```python
@router.get("/customers")
async def list_customers(
    session: AsyncSession = Depends(get_session),
    permission: Permission = Depends(partial(has_permission, "Customer", "read")),
    user: User = Depends(get_current_user)
):
    # 获取行级规则
    conditions = permission.row_rules
    base_stmt = select(Customer)

    for rule in conditions:
        stmt = base_stmt.where(text(rule.condition)).params(user_id=user.id)

    results = await session.execute(stmt)
    customers = results.scalars().all()

    # 字段过滤
    readable_fields = [f.field_name for f in permission.field_permissions if f.can_read]
    return [{f: getattr(c, f) for f in readable_fields} for c in customers]
```
🧱 多层权限设计方案

✅ 一、权限继承模型：基于资源层级或角色层级

---

📌 1. 资源层级（推荐）

适用于模块、子模块、字段等具备结构化的权限。

```python
# models/permission.py（扩展）
class Permission(Base):
    __tablename__ = "permissions"
    id: Mapped[int] = mapped_column(primary_key=True)
    resource: Mapped[str]  # e.g., "Customer", "Customer.Address"
    action: Mapped[str]    # e.g., "read", "write"
    parent_id: Mapped[Optional[int]] = mapped_column(ForeignKey("permissions.id"))
    parent: Mapped[Optional["Permission"]] = relationship(remote_side=[id], backref="children")
```
✅ 优点
•	支持 Customer.read 继承自 Customer.* 或 Customer.Address.read
•	更细粒度控制：模块 > 子模块 > 字段

---

📌 2. 角色层级（可选）
```python
class Role(Base):
    __tablename__ = "roles"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(unique=True)
    parent_id: Mapped[Optional[int]] = mapped_column(ForeignKey("roles.id"))
    parent: Mapped[Optional["Role"]] = relationship(remote_side=[id], backref="children")
```
🧠 说明：
•	子角色自动继承父角色的权限（可以显式递归）
•	适用于企业多岗位（实习生 → 员工 → 管理员）


🛂 权限判定：多层继承逻辑
```python
async def resolve_permission_tree(resource: str, action: str, session: AsyncSession) -> list[str]:
    """
    解析 resource/action 的继承层级
    e.g., Customer.Address.read -> ["Customer.Address.read", "Customer.read", "Customer.*"]
    """
    parts = resource.split(".")
    candidates = []

    for i in range(len(parts), 0, -1):
        res = ".".join(parts[:i])
        candidates.append(f"{res}.{action}")
        candidates.append(f"{res}.*")

    return candidates
```
 
```python
async def has_permission(
    resource: str,
    action: str,
    ...
):
    # 获取所有继承路径
    candidate_resources = await resolve_permission_tree(resource, action, session)

    stmt = select(Permission).join(Role.permissions).join(User.roles).where(
        User.id == user.id,
        or_(*[
            and_(
                Permission.resource == res.split(".")[0],
                Permission.action == res.split(".")[1]
            ) for res in candidate_resources
        ])
    )
    result = await session.execute(stmt)
    ...
```


🔐 示例：权限匹配流程
•	请求访问资源 Customer.Address.read
•	系统自动递归检查权限表：
•	Customer.Address.read
•	Customer.read
•	Customer.*
•	命中任一即视为有权限


🔧 示例资源结构
resource                action            说明
Customer                read              读取客户信息
Customer.Address        read              查看地址
Customer.Address.city   read              查看地址城市字段
Customer.*              *                 客户模块全权限

