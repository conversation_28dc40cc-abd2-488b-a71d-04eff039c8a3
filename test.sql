
-- 插入用户测试数据 (25条)
INSERT INTO res_user (login, password, nickname, email, phone, avatar, sex, summary, lang, tz, last_login, login_ip)
VALUES
     ('admin', '123456', '系统管理员', '<EMAIL>', '13800138001', '/avatar/admin.jpg', 1, '系统管理员账号', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user001', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '张三', '<EMAIL>', '13800138002', '/avatar/zhangsan.jpg', 1, '普通用户张三', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user002', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '李四', '<EMAIL>', '13800138003', '/avatar/lisi.jpg', 2, '普通用户李四', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user003', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '王五', '<EMAIL>', '13800138004', '/avatar/wangwu.jpg', 1, '普通用户王五', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user004', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '赵六', '<EMAIL>', '13800138005', '/avatar/zhaoliu.jpg', 2, '普通用户赵六', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user005', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '钱七', '<EMAIL>', '13800138006', '/avatar/qianqi.jpg', 1, '普通用户钱七', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user006', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '孙八', '<EMAIL>', '13800138007', '/avatar/sunba.jpg', 2, '普通用户孙八', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user007', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '周九', '<EMAIL>', '13800138008', '/avatar/zhoujiu.jpg', 1, '普通用户周九', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user008', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '吴十', '<EMAIL>', '13800138009', '/avatar/wushi.jpg', 2, '普通用户吴十', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user009', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '郑一', '<EMAIL>', '13800138010', '/avatar/zhengyi.jpg', 1, '普通用户郑一', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user010', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '王二', '<EMAIL>', '13800138011', '/avatar/wanger.jpg', 2, '普通用户王二', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user011', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '冯三', '<EMAIL>', '13800138012', '/avatar/fengsan.jpg', 1, '普通用户冯三', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user012', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '陈四', '<EMAIL>', '13800138013', '/avatar/chensi.jpg', 2, '普通用户陈四', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user013', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '褚五', '<EMAIL>', '13800138014', '/avatar/chuwu.jpg', 1, '普通用户褚五', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user014', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '卫六', '<EMAIL>', '13800138015', '/avatar/weiliu.jpg', 2, '普通用户卫六', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user015', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '蒋七', '<EMAIL>', '13800138016', '/avatar/jiangqi.jpg', 1, '普通用户蒋七', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user016', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '沈八', '<EMAIL>', '13800138017', '/avatar/shenba.jpg', 2, '普通用户沈八', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user017', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '韩九', '<EMAIL>', '13800138018', '/avatar/hanjiu.jpg', 1, '普通用户韩九', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user018', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '杨十', '<EMAIL>', '13800138019', '/avatar/yangshi.jpg', 2, '普通用户杨十', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user019', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '朱一', '<EMAIL>', '13800138020', '/avatar/zhuyi.jpg', 1, '普通用户朱一', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user020', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '秦二', '<EMAIL>', '13800138021', '/avatar/qiner.jpg', 2, '普通用户秦二', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user021', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '尤三', '<EMAIL>', '13800138022', '/avatar/yousan.jpg', 1, '普通用户尤三', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user022', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '许四', '<EMAIL>', '13800138023', '/avatar/xusi.jpg', 2, '普通用户许四', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user023', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '何五', '<EMAIL>', '13800138024', '/avatar/hewu.jpg', 1, '普通用户何五', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1'),
     ('user024', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S', '吕六', '<EMAIL>', '13800138025', '/avatar/lvliu.jpg', 2, '普通用户吕六', 'zh_CN', 'Asia/Shanghai', '2023-01-01 10:00:00', '127.0.0.1');

-- 插入角色测试数据 (20条)
INSERT INTO res_role (code, name, description, "order", data_scope)
VALUES
    ('ROLE_ADMIN', '系统管理员', '拥有系统最高权限', 1, 1),
    ('ROLE_USER', '普通用户', '系统普通用户', 2, 3),
    ('ROLE_MANAGER', '部门经理', '管理部门相关事务', 3, 2),
    ('ROLE_HR', '人事专员', '处理人事相关工作', 4, 2),
    ('ROLE_FINANCE', '财务专员', '处理财务相关工作', 5, 3),
    ('ROLE_DEVELOPER', '开发人员', '负责系统开发维护', 6, 3),
    ('ROLE_TESTER', '测试人员', '负责系统测试工作', 7, 3),
    ('ROLE_OPERATOR', '运营人员', '负责系统运营管理', 8, 3),
    ('ROLE_SUPPORT', '客服人员', '负责客户服务支持', 9, 3),
    ('ROLE_AUDITOR', '审计人员', '负责系统审计工作', 10, 3),
    ('ROLE_DESIGNER', '设计师', '负责界面设计工作', 11, 3),
    ('ROLE_ANALYST', '数据分析师', '负责数据分析工作', 12, 3),
    ('ROLE_SALE', '销售人员', '负责销售相关工作', 13, 3),
    ('ROLE_MARKETER', '市场人员', '负责市场推广工作', 14, 3),
    ('ROLE_PM', '项目经理', '负责项目管理工作', 15, 2),
    ('ROLE_CTO', '技术总监', '负责技术管理工作', 16, 1),
    ('ROLE_COO', '运营总监', '负责运营管理', 17, 1),
    ('ROLE_CFO', '财务总监', '负责财务管理', 18, 1),
    ('ROLE_HR_MANAGER', '人事经理', '负责人事管理', 19, 2),
    ('ROLE_SALE_MANAGER', '销售经理', '负责销售管理', 20, 2);

-- 插入权限测试数据 (30条)
INSERT INTO res_permission (code, name, path, method, description, category)
VALUES
     ('PERM_USER_VIEW', '查看用户', '/api/users', 'GET', '查看用户列表', '用户管理'),
     ('PERM_USER_CREATE', '创建用户', '/api/users', 'POST', '创建新用户', '用户管理'),
     ('PERM_USER_UPDATE', '更新用户', '/api/users/*', 'PUT', '更新用户信息', '用户管理'),
     ('PERM_USER_DELETE', '删除用户', '/api/users/*', 'DELETE', '删除用户', '用户管理'),
     ('PERM_ROLE_VIEW', '查看角色', '/api/roles', 'GET', '查看角色列表', '角色管理'),
     ('PERM_ROLE_CREATE', '创建角色', '/api/roles', 'POST', '创建新角色', '角色管理'),
     ('PERM_ROLE_UPDATE', '更新角色', '/api/roles/*', 'PUT', '更新角色信息', '角色管理'),
     ('PERM_ROLE_DELETE', '删除角色', '/api/roles/*', 'DELETE', '删除角色', '角色管理'),
     ('PERM_MENU_VIEW', '查看菜单', '/api/menus', 'GET', '查看菜单列表', '菜单管理'),
     ('PERM_MENU_CREATE', '创建菜单', '/api/menus', 'POST', '创建新菜单', '菜单管理'),
     ('PERM_MENU_UPDATE', '更新菜单', '/api/menus/*', 'PUT', '更新菜单信息', '菜单管理'),
     ('PERM_MENU_DELETE', '删除菜单', '/api/menus/*', 'DELETE', '删除菜单', '菜单管理'),
     ('PERM_DEPT_VIEW', '查看部门', '/api/depts', 'GET', '查看部门列表', '部门管理'),
     ('PERM_DEPT_CREATE', '创建部门', '/api/depts', 'POST', '创建新部门', '部门管理'),
     ('PERM_DEPT_UPDATE', '更新部门', '/api/depts/*', 'PUT', '更新部门信息', '部门管理'),
     ('PERM_DEPT_DELETE', '删除部门', '/api/depts/*', 'DELETE', '删除部门', '部门管理'),
     ('PERM_PERM_VIEW', '查看权限', '/api/permissions', 'GET', '查看权限列表', '权限管理'),
     ('PERM_PERM_CREATE', '创建权限', '/api/permissions', 'POST', '创建新权限', '权限管理'),
     ('PERM_PERM_UPDATE', '更新权限', '/api/permissions/*', 'PUT', '更新权限信息', '权限管理'),
     ('PERM_PERM_DELETE', '删除权限', '/api/permissions/*', 'DELETE', '删除权限', '权限管理'),
     ('PERM_LOG_VIEW', '查看日志', '/api/logs', 'GET', '查看系统日志', '日志管理'),
     ('PERM_REPORT_VIEW', '查看报表', '/api/reports', 'GET', '查看系统报表', '报表管理'),
     ('PERM_CONFIG_VIEW', '查看配置', '/api/configs', 'GET', '查看系统配置', '配置管理'),
     ('PERM_CONFIG_UPDATE', '更新配置', '/api/configs/*', 'PUT', '更新系统配置', '配置管理'),
     ('PERM_BACKUP_VIEW', '查看备份', '/api/backups', 'GET', '查看系统备份', '备份管理'),
     ('PERM_BACKUP_CREATE', '创建备份', '/api/backups', 'POST', '创建系统备份', '备份管理'),
     ('PERM_BACKUP_DELETE', '删除备份', '/api/backups/*', 'DELETE', '删除系统备份', '备份管理'),
     ('PERM_MONITOR_VIEW', '查看监控', '/api/monitor', 'GET', '查看系统监控', '监控管理'),
     ('PERM_DASHBOARD_VIEW', '查看仪表板', '/api/dashboard', 'GET', '查看系统仪表板', '仪表板'),
     ('PERM_PROFILE_UPDATE', '更新个人信息', '/api/profile', 'PUT', '更新个人信息', '个人设置');

-- 插入菜单测试数据 (25条)
INSERT INTO res_menu (name, menu_type, icon, route_name, route_path, component_path, redirect, meta, "order", description)
VALUES
   ('系统管理', 'menu', 'setting', 'System', '/system', NULL, '/system/user', '{"title": "系统管理", "icon": "setting"}', 1, '系统管理目录'),
   ('用户管理', 'menu', 'user', 'User', '/system/user', '/system/user/index', NULL, '{"title": "用户管理", "icon": "user"}', 1, '用户管理菜单'),
   ('角色管理', 'menu', 'team', 'Role', '/system/role', '/system/role/index', NULL, '{"title": "角色管理", "icon": "team"}', 2, '角色管理菜单'),
   ('菜单管理', 'menu', 'menu', 'Menu', '/system/menu', '/system/menu/index', NULL, '{"title": "菜单管理", "icon": "menu"}', 3, '菜单管理菜单'),
   ('部门管理', 'menu', 'apartment', 'Dept', '/system/dept', '/system/dept/index', NULL, '{"title": "部门管理", "icon": "apartment"}', 4, '部门管理菜单'),
   ('权限管理', 'menu', 'safety', 'Permission', '/system/permission', '/system/permission/index', NULL, '{"title": "权限管理", "icon": "safety"}', 5, '权限管理菜单'),
   ('系统监控', 'menu', 'monitor', 'Monitor', '/monitor', NULL, '/monitor/online', '{"title": "系统监控", "icon": "monitor"}', 2, '系统监控目录'),
   ('在线用户', 'menu', 'user-switch', 'Online', '/monitor/online', '/monitor/online/index', NULL, '{"title": "在线用户", "icon": "user-switch"}', 1, '在线用户菜单'),
   ('操作日志', 'menu', 'file-search', 'OperLog', '/monitor/operlog', '/monitor/operlog/index', NULL, '{"title": "操作日志", "icon": "file-search"}', 2, '操作日志菜单'),
   ('登录日志', 'menu', 'login', 'LoginLog', '/monitor/loginlog', '/monitor/loginlog/index', NULL, '{"title": "登录日志", "icon": "login"}', 3, '登录日志菜单'),
   ('系统工具', 'menu', 'tool', 'Tool', '/tool', NULL, '/tool/build', '{"title": "系统工具", "icon": "tool"}', 3, '系统工具目录'),
   ('表单构建', 'menu', 'build', 'Build', '/tool/build', '/tool/build/index', NULL, '{"title": "表单构建", "icon": "build"}', 1, '表单构建菜单'),
   ('代码生成', 'menu', 'code', 'Gen', '/tool/gen', '/tool/gen/index', NULL, '{"title": "代码生成", "icon": "code"}', 2, '代码生成菜单'),
   ('系统接口', 'menu', 'api', 'Swagger', '/tool/swagger', '/tool/swagger/index', NULL, '{"title": "系统接口", "icon": "api"}', 3, '系统接口菜单'),
   ('个人中心', 'menu', 'user', 'Profile', '/profile', '/profile/index', NULL, '{"title": "个人中心", "icon": "user"}', 4, '个人中心菜单'),
   ('消息中心', 'menu', 'message', 'Message', '/message', '/message/index', NULL, '{"title": "消息中心", "icon": "message"}', 5, '消息中心菜单'),
   ('工作台', 'menu', 'dashboard', 'Dashboard', '/dashboard', '/dashboard/index', NULL, '{"title": "工作台", "icon": "dashboard"}', 0, '工作台菜单'),
   ('首页', 'menu', 'home', 'Home', '/home', '/home/<USER>', NULL, '{"title": "首页", "icon": "home"}', 0, '首页菜单'),
   ('关于', 'menu', 'info', 'About', '/about', '/about/index', NULL, '{"title": "关于我们", "icon": "info"}', 6, '关于菜单'),
   ('帮助', 'menu', 'question', 'Help', '/help', '/help/index', NULL, '{"title": "帮助文档", "icon": "question"}', 7, '帮助菜单'),
   ('通知公告', 'menu', 'notification', 'Notice', '/system/notice', '/system/notice/index', NULL, '{"title": "通知公告", "icon": "notification"}', 6, '通知公告菜单'),
   ('字典管理', 'menu', 'read', 'Dict', '/system/dict', '/system/dict/index', NULL, '{"title": "字典管理", "icon": "read"}', 7, '字典管理菜单'),
   ('参数配置', 'menu', 'control', 'Config', '/system/config', '/system/config/index', NULL, '{"title": "参数配置", "icon": "control"}', 8, '参数配置菜单'),
   ('日志管理', 'menu', 'file-text', 'Log', '/system/log', '/system/log/index', NULL, '{"title": "日志管理", "icon": "file-text"}', 9, '日志管理菜单'),
   ('任务调度', 'menu', 'schedule', 'Job', '/system/job', '/system/job/index', NULL, '{"title": "任务调度", "icon": "schedule"}', 10, '任务调度菜单');

-- 插入部门测试数据 (20条)
INSERT INTO res_dept (name, "order", parent_id)
VALUES
    ('总公司', 1, NULL),
    ('北京分公司', 1, 1),
    ('上海分公司', 2, 1),
    ('深圳分公司', 3, 1),
    ('技术部', 1, 2),
    ('销售部', 2, 2),
    ('市场部', 3, 2),
    ('人事部', 4, 2),
    ('财务部', 5, 2),
    ('技术部', 1, 3),
    ('销售部', 2, 3),
    ('市场部', 3, 3),
    ('人事部', 4, 3),
    ('财务部', 5, 3),
    ('技术部', 1, 4),
    ('销售部', 2, 4),
    ('市场部', 3, 4),
    ('人事部', 4, 4),
    ('财务部', 5, 4),
    ('运维部', 6, 1);
